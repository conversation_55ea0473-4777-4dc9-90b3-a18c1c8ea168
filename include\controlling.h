#ifndef CONTROLLING_H
#define CONTROLLING_H

#include <Arduino.h>
#include "menu.h"

// Pin definitions untuk XY6020 Modbus
#define XY6020_RX_PIN 16  // GPIO16 untuk ESP32
#define XY6020_TX_PIN 17  // GPIO17 untuk ESP32

// Sensor pin definitions moved to sensor.h

// Pin definitions untuk LCD I2C
#define LCD_SDA_PIN 21          // GPIO21 - I2C SDA
#define LCD_SCL_PIN 22          // GPIO22 - I2C SCL

// Pin definitions untuk Fan Motor Driver (L298N)
#define FAN_MOTOR_EN_PIN 14     // GPIO14 - Enable pin (PWM speed control)
#define FAN_MOTOR_IN1_PIN 27    // GPIO27 - Direction control 1
#define FAN_MOTOR_IN2_PIN 26    // GPIO26 - Direction control 2

// Pin definitions untuk Water Pump Motor Driver (L298N)
#define PUMP_MOTOR_EN_PIN 5     // GPIO5 - Enable pin (PWM speed control)
#define PUMP_MOTOR_IN1_PIN 18   // GPIO18 - Direction control 1
#define PUMP_MOTOR_IN2_PIN 19   // GPIO19 - Direction control 2

// PWM settings untuk motor driver
#define PWM_FREQUENCY 1000
#define PWM_RESOLUTION 8
#define PWM_CHANNEL_PUMP 0
#define PWM_CHANNEL_FAN 1

// ADC definitions moved to sensor.h

// Struktur untuk status sistem
struct SystemStatus {
    bool isRunning;
    bool pumpActive;
    bool fanActive;
    bool xy6020Connected;
    unsigned long processStartTime;
    unsigned long lastSensorRead;
    unsigned long lastControlUpdate;
};

// Struktur untuk sensor readings
struct SensorData {
    float pH;
    float temperature;
    float gasLevel;     // MQ sensor reading
    float voltage;      // XY6020 voltage
    float current;      // XY6020 current
    float power;        // XY6020 power
    unsigned long lastUpdate;
};

// Struktur untuk control settings
struct ControlSettings {
    float targetVoltage;
    float targetCurrent;
    int pumpSpeed;      // 0-100%
    int fanSpeed;       // 0-100%
    int pumpTime;       // minutes
    int fanTime;        // minutes
    float pHMin;
    float pHMax;
    float tempMin;
    float tempMax;
};

// External variables yang akan digunakan dari main.cpp
extern SystemStatus systemStatus;
extern SensorData sensorData;
extern ControlSettings controlSettings;

// Deklarasi fungsi controlling
void initializeControlling();
void controllingTask();

// Sensor functions (moved to sensor.h/cpp)
void updateXY6020Sensors();  // Update XY6020 sensor data only

// Actuator control functions
void initializeActuators();
void setPumpSpeed(int speed);  // 0-100%
void setFanSpeed(int speed);   // 0-100%
void setPumpDirection(bool forward);  // true=forward, false=reverse
void setFanDirection(bool forward);   // true=forward, false=reverse
void emergencyStopMotors();
void stopAllActuators();

// XY6020 control functions
void initializeXY6020();
void xy6020Task();
bool setXY6020Voltage(float voltage);
bool setXY6020Current(float current);
bool setXY6020Output(bool enabled);
float getXY6020Voltage();
float getXY6020Current();
float getXY6020Power();
bool isXY6020Connected();

// Process control functions
void startElectrowiningProcess();
void stopElectrowiningProcess();
void updateProcessControl();
bool checkSafetyLimits();

// Profile management functions
void loadProfileSettings(const UserProfile& profile);
void applyManualSettings();

// Utility functions
void logSystemStatus();
void handleEmergencyStop();

#endif // CONTROLLING_H
