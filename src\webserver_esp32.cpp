#include "webserver_esp32.h"

#include <ArduinoJson.h>



XyWebServer::XyWebServer(Xy6020 *xy_obj, Settings &config)
    : mXy(xy_obj), mConfig(config) {
  server = new WebServer(80);
}

void XyWebServer::init(bool admin_mode) {
  Serial.print("MAC: ");
  Serial.println(WiFi.macAddress());
  Serial.print("Got IP: ");
  Serial.println(WiFi.localIP());

  server->on("/style.css", std::bind(&XyWebServer::handleStyleCss, this));
  if (admin_mode) {
    server->on("/", std::bind(&XyWebServer::handleSettings, this));
    server->on("/index.html", std::bind(&XyWebServer::handleRoot, this));
  } else {
    server->on("/", std::bind(&XyWebServer::handleRoot, this));
    server->on("/index.html", std::bind(&XyWebServer::handleRoot, this));
  }
  server->on("/settings.html", std::bind(&XyWebServer::handleSettings, this));
  server->on("/charts.html", std::bind(&XyWebServer::handleCharts, this));
  server->on("/logic.js", std::bind(&XyWebServer::handleLogicJs, this));
  server->on("/segment-display.js",
                std::bind(&XyWebServer::handleSegmentDisplayJs, this));
  server->on("/charts.js", std::bind(&XyWebServer::handleChartsJs, this));
  server->onNotFound(std::bind(&XyWebServer::handleNotFound, this));
  server->on("/control", HTTP_GET, std::bind(&XyWebServer::handleControlGet, this));
  server->on("/control", HTTP_POST, std::bind(&XyWebServer::handleControlPost, this));
  server->on("/control", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  server->on("/config", HTTP_GET, std::bind(&XyWebServer::handleSettingsGet, this));
  server->on("/config", HTTP_POST, std::bind(&XyWebServer::handleSettingsPost, this));
  server->on("/config", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  server->on("/wifi-status", HTTP_GET, std::bind(&XyWebServer::handleWifiStatus, this));
  server->on("/wifi-status", HTTP_OPTIONS, std::bind(&XyWebServer::handleCORS, this));

  // Debug info
  Serial.println("Routes registered:");
  Serial.println(" - GET /");
  Serial.println(" - GET /index.html");
  Serial.println(" - GET /settings.html");
  Serial.println(" - GET /charts.html");
  Serial.println(" - GET /style.css");
  Serial.println(" - GET /logic.js");
  Serial.println(" - GET /segment-display.js");
  Serial.println(" - GET /charts.js");
  Serial.println(" - GET /control");
  Serial.println(" - POST /control");
  Serial.println(" - OPTIONS /control");
  Serial.println(" - GET /config");
  Serial.println(" - POST /config");
  Serial.println(" - OPTIONS /config");
  Serial.println(" - GET /wifi-status");
  Serial.println(" - OPTIONS /wifi-status");
  server->begin();
  Serial.println("HTTP server started");
}

void XyWebServer::task() { server->handleClient(); }

void XyWebServer::handleNotFound() {
  Serial.println("handleNotFound called");
  Serial.print("URI: ");
  Serial.println(server->uri());
  Serial.print("Method: ");
  Serial.println(server->method() == HTTP_GET ? "GET" : (server->method() == HTTP_POST ? "POST" : "OTHER"));
  Serial.print("Arguments: ");
  Serial.println(server->args());

  for (int i = 0; i < server->args(); i++) {
    Serial.print(server->argName(i));
    Serial.print(": ");
    Serial.println(server->arg(i));
  }

  server->send(404, "text/plain", "Not found");
}

void XyWebServer::handleRoot() { server->send(200, "text/html", html__index); }

void XyWebServer::handleSettings() {
  server->send(200, "text/html", html__settings);
}

void XyWebServer::handleStyleCss() {
  server->send(200, "text/css", css__style);
}

void XyWebServer::handleLogicJs() {
  server->send(200, "text/javascript", js__logic);
}

void XyWebServer::handleSegmentDisplayJs() {
  server->send(200, "text/javascript", js__segmentdisplay);
}

void XyWebServer::handleCharts() {
  server->send(200, "text/html", html__charts);
}

void XyWebServer::handleChartsJs() {
  server->send(200, "text/javascript", js__charts);
}

void XyWebServer::handleCORS() {
  Serial.println("handleCORS called");
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server->sendHeader("Access-Control-Allow-Headers", "Content-Type, X-Requested-With");
  server->sendHeader("Access-Control-Max-Age", "86400");
  server->send(204);
}

void XyWebServer::handleWifiStatus() {
  Serial.println("handleWifiStatus called");

  String status = "Not Connected";
  String ip = "-";
  String ssid = "-";

  if (WiFi.status() == WL_CONNECTED) {
    status = "Connected";
    ip = WiFi.localIP().toString();
    ssid = WiFi.SSID();
  }

  String response = "{";
  response += "\"status\":\"" + status + "\",";
  response += "\"ip\":\"" + ip + "\",";
  response += "\"ssid\":\"" + ssid + "\"";
  response += "}";

  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->send(200, "application/json", response);
}

void XyWebServer::handleSettingsGet() {
  String str;
  auto &cfg = mConfig.data();

  String mqtt_server;
  if (strlen(cfg.mqtt_broker_host) > 0) {
    mqtt_server = String(cfg.mqtt_broker_host);
  } else {
    mqtt_server = IPAddress(cfg.mqtt_broker).toString();
  }

  str += "{\"ssid\" : \"" + String(cfg.wifi_ssid) + "\"," +
         "\"use-static-ip\": " + cfg.use_static_ip + ",\"static-ip\":\"" +
         IPAddress(cfg.static_ip).toString() + "\",\"subnet\": \"" +
         IPAddress(cfg.subnet).toString() + "\",\"gateway\": \"" +
         IPAddress(cfg.gateway).toString() + "\",\"mqtt-server\": \"" +
         mqtt_server + "\",\"mqtt-port\": \"" +
         cfg.mqtt_port + "\",\"mqtt-user\": \"" + cfg.mqtt_user +
         "\", \"mqtt-id\": \"" + cfg.mqtt_id +
         "\", \"mqtt-pub-topic\": \"" + cfg.mqtt_pub_topic +
         "\", \"mqtt-sub-topic\": \"" + cfg.mqtt_sub_topic +
         "\",\"zero-feed-in\": " + cfg.zero_feed_in + ",\"smi-topic\": \"" +
         cfg.smi_topic + "\",\"sm-name\": \"" + cfg.sm_name +
         "\", \"enable-input-limits\":" + cfg.enable_input_limits +
         ", \"switch-off-voltage\": \"" + cfg.switch_off_voltage +
         "\", \"switch-on-voltage\": \"" + cfg.switch_on_voltage + "\"}";

  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->send(200, "application/json", str);
}

void XyWebServer::handleControlGet() {
  char str[256];
  bool connected = mXy->isConnected();

  if (connected) {
    // Device is connected, send actual values
    sprintf(str,
            "{"
            "\"voltage\": %0.2f,"
            "\"current\": %0.2f,"
            "\"power\": %0.1f,"
            "\"output\": %d,"
            "\"tvoltage\": %0.2f,"
            "\"tcurrent\": %0.2f,"
            "\"tpower\": %0.1f,"
            "\"ivoltage\": %0.1f,"
            "\"connected\": 1"
            "}",
            mXy->actualVoltage(), mXy->actualCurrent(), mXy->actualPower(),
            mXy->outputEnabled(), mXy->targetVoltage(), mXy->maxCurrent(),
            mXy->maxPower(), mXy->inputVoltage());
  } else {
    // Device is not connected, send zeros with connected=0
    sprintf(str,
            "{"
            "\"voltage\": 0.00,"
            "\"current\": 0.00,"
            "\"power\": 0.0,"
            "\"output\": 0,"
            "\"tvoltage\": 0.00,"
            "\"tcurrent\": 0.00,"
            "\"tpower\": 0.0,"
            "\"ivoltage\": 0.0,"
            "\"connected\": 0"
            "}");
  }

  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->send(200, "application/json", str);
}

void XyWebServer::handleControlPost() {
  Serial.println("Control set request received.");
  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Content-Type", "application/json");

  // Check if XY6020L is connected
  if (!mXy->isConnected()) {
    Serial.println("Error: Cannot process control request - XY6020L not connected");
    server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"XY6020L not connected\"}");
    return;
  }

  for (int a = 0; a < server->args(); ++a) {
    auto param = server->argName(a);
    auto val_str = server->arg(a);
    float value = val_str.toFloat();
    Serial.printf_P("%s=%0.2f\n", param.c_str(), value);
    bool ret = false;

    if (param == "voltage") {
      Serial.printf_P("Setting target voltage to %.2fV\n", value);
      ret = mXy->setTargetVoltage(value);
      if (!ret) {
        Serial.println("Failed to set target voltage");
        server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid voltage value\"}");
        return;
      }
    } else if (param == "current") {
      Serial.printf_P("Setting max current to %.2fA\n", value);
      ret = mXy->setMaxCurrent(value);
      if (!ret) {
        Serial.println("Failed to set max current");
        server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid current value\"}");
        return;
      }
    } else if (param == "max-power") {
      Serial.printf_P("Setting max power to %.1fW\n", value);
      ret = mXy->setMaxPower(value);
      mConfig.store();
      if (!ret) {
        Serial.println("Failed to set max power");
        server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"Invalid power value\"}");
        return;
      }
    } else if (param == "output") {
      if (val_str.length()) {
        bool outputState = (value > 0.01);
        Serial.printf_P("Setting output to %s\n", outputState ? "ON" : "OFF");
        mXy->setOutputEnabled(outputState);
        ret = true;
      }
    } else if (param == "reset") {
      Serial.println("Restarting ESP32...");
      server->send(200, "application/json", "{\"status\":\"OK\",\"message\":\"Restarting\"}");
      delay(100);
      ESP.restart();
      return;
    } else {
      Serial.printf_P("Unknown parameter: %s\n", param.c_str());
      server->send(200, "application/json", "{\"status\":\"FAIL\",\"message\":\"Unknown parameter\"}");
      return;
    }
  }

  // Send success response
  server->send(200, "application/json", "{\"status\":\"OK\"}");

  // Force a delay to allow the response to be sent before any potential issues
  delay(50);
}

void XyWebServer::handleSettingsPost() {
  Serial.println("handleSettingsPost called");
  Serial.print("Number of args: ");
  Serial.println(server->args());

  for (int i = 0; i < server->args(); i++) {
    Serial.print(server->argName(i));
    Serial.print(": ");
    Serial.println(server->arg(i));
  }

  // Get the raw POST data
  String postBody = server->arg("plain");
  Serial.print("Raw POST data: ");
  Serial.println(postBody);

  JsonDocument doc;
  DeserializationError err = deserializeJson(doc, postBody);

  if (err) {
    Serial.print("deserializeJson() failed: ");
    Serial.println(err.c_str());
    server->sendHeader("Access-Control-Allow-Origin", "*");
    server->send(400, "text/plain", String("JSON parsing failed: ") + err.c_str());
    return;
  }

  auto &cfg = mConfig.data();
  if (err == DeserializationError::Ok) {
    if (!doc["ssid"].isUnbound()) {
      String str = doc["ssid"];
      strncpy(cfg.wifi_ssid, str.c_str(), 128);
    }
    if (!doc["wifi-password"].isUnbound()) {
      String str = doc["wifi-password"];
      if (str.length()) {
        strncpy(cfg.wifi_password, str.c_str(), 128);
      }
    }

    cfg.use_static_ip = doc["use-static-ip"] | cfg.use_static_ip;

    if (!doc["static-ip"].isUnbound()) {
      String str = doc["static-ip"];
      IPAddress addr;
      if (addr.fromString(str)) {
        cfg.static_ip = addr;
      }
    }

    if (!doc["subnet"].isUnbound()) {
      String str = doc["subnet"];
      IPAddress addr;
      if (addr.fromString(str)) {
        cfg.subnet = addr;
      }
    }

    if (!doc["gateway"].isUnbound()) {
      String str = doc["gateway"];
      IPAddress addr;
      if (addr.fromString(str)) {
        cfg.gateway = addr;
      }
    }

    if (!doc["mqtt-server"].isUnbound()) {
      String str = doc["mqtt-server"];
      // Coba parse sebagai IP address
      IPAddress addr;
      if (addr.fromString(str)) {
        cfg.mqtt_broker = addr;
        // Jika berhasil di-parse sebagai IP, kosongkan host string
        cfg.mqtt_broker_host[0] = '\0';
      } else {
        // Jika bukan IP address, simpan sebagai host string
        strncpy(cfg.mqtt_broker_host, str.c_str(), 128);
        // Reset IP address
        cfg.mqtt_broker = 0;
      }
    }

    cfg.mqtt_port = doc["mqtt-port"] | cfg.mqtt_port;

    if (!doc["mqtt-user"].isUnbound()) {
      String str = doc["mqtt-user"];
      strncpy(cfg.mqtt_user, str.c_str(), 128);
    }

    if (!doc["mqtt-pass"].isUnbound()) {
      String str = doc["mqtt-pass"];
      if (str.length()) {
        strncpy(cfg.mqtt_password, str.c_str(), 128);
      }
    }

    if (!doc["mqtt-id"].isUnbound()) {
      String str = doc["mqtt-id"];
      strncpy(cfg.mqtt_id, str.c_str(), 128);
    }

    if (!doc["mqtt-pub-topic"].isUnbound()) {
      String str = doc["mqtt-pub-topic"];
      strncpy(cfg.mqtt_pub_topic, str.c_str(), 128);
    }

    if (!doc["mqtt-sub-topic"].isUnbound()) {
      String str = doc["mqtt-sub-topic"];
      strncpy(cfg.mqtt_sub_topic, str.c_str(), 128);
    }

    cfg.zero_feed_in = doc["zero-feed-in"] | cfg.zero_feed_in;

    if (!doc["smi-topic"].isUnbound()) {
      String str = doc["smi-topic"];
      strncpy(cfg.smi_topic, str.c_str(), 128);
    }
    if (!doc["sm-name"].isUnbound()) {
      String str = doc["sm-name"];
      strncpy(cfg.sm_name, str.c_str(), 128);
    }

    cfg.enable_input_limits =
        doc["enable-input-limits"] | cfg.enable_input_limits;

    if (!doc["switch-off-voltage"].isUnbound()) {
      String str = doc["switch-off-voltage"];
      cfg.switch_off_voltage = str.toFloat();
    }
    if (!doc["switch-on-voltage"].isUnbound()) {
      String str = doc["switch-on-voltage"];
      cfg.switch_on_voltage = str.toFloat();
    }

    mConfig.store();
  }

  server->sendHeader("Access-Control-Allow-Origin", "*");
  server->sendHeader("Content-Type", "application/json");
  server->send(200, "application/json", "{\"status\":\"OK\"}");

  // Check if WiFi settings were changed
  bool wifiSettingsChanged = false;
  if (!doc["ssid"].isUnbound() || !doc["wifi-password"].isUnbound()) {
    wifiSettingsChanged = true;
  }

  // If WiFi settings were changed, restart ESP32 after a short delay
  if (wifiSettingsChanged) {
    Serial.println("WiFi settings changed. Restarting in 2 seconds...");
    delay(500); // Give time for the HTTP response to be sent
    ESP.restart();
  }
}
