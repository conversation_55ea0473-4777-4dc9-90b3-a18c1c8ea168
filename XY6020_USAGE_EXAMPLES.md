# XY6020 Usage Examples untuk Elektrowining
## Contoh Praktis Berdasarkan xy6020_wifi_ctlr_esp_V1.5

### Basic Usage Example

#### 1. Setup dan Initialization
```cpp
#include "xy6020.h"
#include "controlling.h"

void setup() {
    Serial.begin(115200);
    
    // Initialize controlling system (includes XY6020)
    initializeControlling();
    
    Serial.println("XY6020 Elektrowining System Ready");
}

void loop() {
    // CRITICAL: Must call this regularly for communication
    controllingTask();
    
    // Your other code here
    delay(50);
}
```

#### 2. Basic Control Operations
```cpp
void basicControlExample() {
    // Check if XY6020 is connected
    if (isXY6020Connected()) {
        Serial.println("XY6020 Connected - Starting control sequence");
        
        // Set voltage and current for elektrowining
        setXY6020Voltage(12.0);    // 12V for copper elektrowining
        setXY6020Current(2.5);     // 2.5A max current
        
        // Enable output
        setXY6020Output(true);
        
        // Monitor for 10 seconds
        for (int i = 0; i < 100; i++) {
            controllingTask();  // Keep communication alive
            
            Serial.printf("V: %.2fV, I: %.2fA, P: %.1fW\n", 
                         getXY6020Voltage(), 
                         getXY6020Current(), 
                         getXY6020Power());
            
            delay(100);
        }
        
        // Disable output
        setXY6020Output(false);
        Serial.println("Control sequence completed");
    } else {
        Serial.println("XY6020 not connected!");
    }
}
```

### Elektrowining Process Control

#### 1. Start Elektrowining Process
```cpp
void startElektrowiningExample() {
    Serial.println("Starting Elektrowining Process...");
    
    // Set process parameters
    controlSettings.targetVoltage = 15.0;  // 15V
    controlSettings.targetCurrent = 3.0;   // 3A
    controlSettings.pumpSpeed = 50;        // 50% pump speed
    controlSettings.fanSpeed = 30;         // 30% fan speed
    
    // Start the process
    startElectrowiningProcess();
    
    Serial.println("Elektrowining process started successfully");
}
```

#### 2. Monitor Process
```cpp
void monitorElektrowiningProcess() {
    static unsigned long lastPrint = 0;
    
    // Print status every 5 seconds
    if (millis() - lastPrint >= 5000) {
        lastPrint = millis();
        
        if (systemStatus.isRunning) {
            Serial.println("=== Elektrowining Status ===");
            Serial.printf("XY6020 Connected: %s\n", 
                         systemStatus.xy6020Connected ? "Yes" : "No");
            
            if (systemStatus.xy6020Connected) {
                Serial.printf("Voltage: %.2fV (Target: %.2fV)\n", 
                             sensorData.voltage, controlSettings.targetVoltage);
                Serial.printf("Current: %.2fA (Max: %.2fA)\n", 
                             sensorData.current, controlSettings.targetCurrent);
                Serial.printf("Power: %.1fW\n", sensorData.power);
                Serial.printf("pH: %.2f, Temp: %.2f°C\n", 
                             sensorData.pH, sensorData.temperature);
            }
            
            Serial.printf("Pump: %s, Fan: %s\n",
                         systemStatus.pumpActive ? "ON" : "OFF",
                         systemStatus.fanActive ? "ON" : "OFF");
            Serial.println("===========================");
        }
    }
}
```

#### 3. Safety Monitoring
```cpp
void safetyMonitoringExample() {
    // Check safety limits
    if (systemStatus.isRunning) {
        // Voltage safety check
        if (sensorData.voltage > 20.0) {
            Serial.println("SAFETY ALERT: Voltage too high!");
            handleEmergencyStop();
            return;
        }
        
        // Current safety check
        if (sensorData.current > 5.0) {
            Serial.println("SAFETY ALERT: Current too high!");
            handleEmergencyStop();
            return;
        }
        
        // Power safety check
        if (sensorData.power > 100.0) {
            Serial.println("SAFETY ALERT: Power too high!");
            handleEmergencyStop();
            return;
        }
        
        // Connection safety check
        if (!systemStatus.xy6020Connected) {
            Serial.println("SAFETY ALERT: XY6020 disconnected!");
            handleEmergencyStop();
            return;
        }
    }
}
```

### Advanced Control Examples

#### 1. Ramping Voltage Control
```cpp
void rampVoltageExample() {
    Serial.println("Starting voltage ramp...");
    
    float startVoltage = 0.0;
    float targetVoltage = 12.0;
    float step = 0.5;
    unsigned long stepDelay = 2000; // 2 seconds per step
    
    for (float v = startVoltage; v <= targetVoltage; v += step) {
        if (!isXY6020Connected()) {
            Serial.println("Connection lost during ramp!");
            break;
        }
        
        setXY6020Voltage(v);
        Serial.printf("Voltage set to: %.1fV\n", v);
        
        // Wait and monitor
        unsigned long startTime = millis();
        while (millis() - startTime < stepDelay) {
            controllingTask();  // Keep communication alive
            delay(50);
        }
    }
    
    Serial.println("Voltage ramp completed");
}
```

#### 2. Pulse Control for Elektrowining
```cpp
void pulseControlExample() {
    Serial.println("Starting pulse control...");
    
    float pulseVoltage = 15.0;
    float restVoltage = 5.0;
    unsigned long pulseTime = 5000;  // 5 seconds ON
    unsigned long restTime = 2000;   // 2 seconds LOW
    int cycles = 10;
    
    for (int cycle = 0; cycle < cycles; cycle++) {
        if (!isXY6020Connected()) {
            Serial.println("Connection lost during pulse control!");
            break;
        }
        
        // Pulse phase
        Serial.printf("Cycle %d: Pulse ON (%.1fV)\n", cycle + 1, pulseVoltage);
        setXY6020Voltage(pulseVoltage);
        setXY6020Output(true);
        
        unsigned long startTime = millis();
        while (millis() - startTime < pulseTime) {
            controllingTask();
            delay(50);
        }
        
        // Rest phase
        Serial.printf("Cycle %d: Rest (%.1fV)\n", cycle + 1, restVoltage);
        setXY6020Voltage(restVoltage);
        
        startTime = millis();
        while (millis() - startTime < restTime) {
            controllingTask();
            delay(50);
        }
    }
    
    // Turn off
    setXY6020Output(false);
    Serial.println("Pulse control completed");
}
```

#### 3. Adaptive Current Control
```cpp
void adaptiveCurrentControl() {
    static unsigned long lastAdjustment = 0;
    
    if (systemStatus.isRunning && isXY6020Connected()) {
        // Adjust current every 10 seconds based on conditions
        if (millis() - lastAdjustment >= 10000) {
            lastAdjustment = millis();
            
            float currentSetting = controlSettings.targetCurrent;
            
            // Increase current if voltage is stable and low
            if (sensorData.voltage < controlSettings.targetVoltage * 0.9 && 
                sensorData.current < currentSetting * 0.8) {
                currentSetting += 0.2;
                Serial.println("Increasing current limit");
            }
            
            // Decrease current if temperature is high
            if (sensorData.temperature > 35.0) {
                currentSetting -= 0.3;
                Serial.println("Decreasing current due to high temperature");
            }
            
            // Apply limits
            currentSetting = constrain(currentSetting, 0.5, 5.0);
            
            if (currentSetting != controlSettings.targetCurrent) {
                setXY6020Current(currentSetting);
                Serial.printf("Current adjusted to: %.2fA\n", currentSetting);
            }
        }
    }
}
```

### Error Handling Examples

#### 1. Connection Recovery
```cpp
void connectionRecoveryExample() {
    static unsigned long lastConnectionCheck = 0;
    static bool wasConnected = true;
    
    if (millis() - lastConnectionCheck >= 1000) {
        lastConnectionCheck = millis();
        
        bool currentlyConnected = isXY6020Connected();
        
        if (!currentlyConnected && wasConnected) {
            Serial.println("XY6020 connection lost! Stopping process...");
            stopElectrowiningProcess();
        } else if (currentlyConnected && !wasConnected) {
            Serial.println("XY6020 connection restored!");
            // Optionally restart process or notify user
        }
        
        wasConnected = currentlyConnected;
    }
}
```

#### 2. Complete Example Integration
```cpp
void completeElektrowiningExample() {
    // This should be called in main loop
    controllingTask();  // CRITICAL: Keep XY6020 communication alive
    
    // Monitor safety
    safetyMonitoringExample();
    
    // Monitor connection
    connectionRecoveryExample();
    
    // Monitor process
    monitorElektrowiningProcess();
    
    // Adaptive control (if enabled)
    if (systemStatus.isRunning) {
        adaptiveCurrentControl();
    }
}
```

### Integration dengan Menu System

```cpp
// Dalam menu handling
void handleVoltageControlMenu() {
    if (menuState.isEditing) {
        // User is editing voltage value
        float newVoltage = getEncoderValue() / 10.0;  // 0.1V steps
        newVoltage = constrain(newVoltage, 0.0, 60.0);
        
        if (isXY6020Connected()) {
            setXY6020Voltage(newVoltage);
            controlSettings.targetVoltage = newVoltage;
        }
    }
}

void handleCurrentControlMenu() {
    if (menuState.isEditing) {
        // User is editing current value
        float newCurrent = getEncoderValue() / 100.0;  // 0.01A steps
        newCurrent = constrain(newCurrent, 0.0, 20.0);
        
        if (isXY6020Connected()) {
            setXY6020Current(newCurrent);
            controlSettings.targetCurrent = newCurrent;
        }
    }
}
```

### Tips untuk Implementasi

1. **Selalu panggil controllingTask()** di main loop
2. **Check connection status** sebelum melakukan control operations
3. **Gunakan constrain()** untuk memastikan nilai dalam range
4. **Monitor safety parameters** secara kontinyu
5. **Implement graceful shutdown** saat terjadi error
6. **Log semua operations** untuk debugging
7. **Test dengan load yang sebenarnya** sebelum production

Contoh-contoh ini menunjukkan implementasi praktis berdasarkan xy6020_wifi_ctlr_esp_V1.5 yang telah terbukti reliable untuk aplikasi elektrowining.
