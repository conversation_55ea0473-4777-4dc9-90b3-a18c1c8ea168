Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FE8E
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210286019, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E80  000210068E24 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9160  00021006A225 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF48960000 ntdll.dll
7FFF47B00000 KERNEL32.DLL
7FFF461F0000 KERNELBASE.dll
7FFF475E0000 USER32.dll
7FFF461C0000 win32u.dll
7FFF48830000 GDI32.dll
7FFF45E70000 gdi32full.dll
7FFF45C70000 msvcp_win.dll
7FFF45D20000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF48860000 advapi32.dll
7FFF47A40000 msvcrt.dll
7FFF48780000 sechost.dll
7FFF467E0000 RPCRT4.dll
7FFF450E0000 CRYPTBASE.DLL
7FFF46680000 bcryptPrimitives.dll
7FFF484D0000 IMM32.DLL
