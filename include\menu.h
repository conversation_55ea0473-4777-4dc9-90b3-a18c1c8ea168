#ifndef MENU_H
#define MENU_H
#include <Arduino.h>

enum MenuLevel {
    LEVEL_STATUS = 0,
    LEVEL_START,
    LEVEL_SETUP,
    LEVEL_MANUAL_MODE,
    LEVEL_AUTO_MODE,
    LEVEL_MANUAL_SETTINGS,
    LEVEL_CONFIG_PROFILES,
    LEVEL_SENSOR_SETTINGS,
    LEVEL_OUTPUT_SETTINGS,
    LEVEL_EDIT_VALUES,
    LEVEL_EDIT_pH,
    LEVEL_EDIT_Temp,
    LEVEL_EDIT_Gas,
    SetMinpH,
    SetMaxpH,
    SetMinTemp,
    SetMaxTemp,
    SetMinGas,
    SetMaxGas,
    PumpSetting,
    FanSetting,
    VoltageControl,
    CurrentControl,
    PumpSpeed,
    PumpTime,
    FanSpeed,
    FanTime,
    voltSet,
    curSet,
    NewUser,
    userA,
    resetProfile,
    SaveProfile,
    startProfile,
    nameUser,
    typeUser,
    LEVEL_ADD_USER,
    deleteProfile,
    updateProfil,
    risetDefault,
    autoMode,
    LEVEL_chooseProfile
};

enum MenuAction {
    ACTION_NONE,
    ACTION_ENTER,
    ACTION_BACK,
    ACTION_START,
    ACTION_STOP,
    ACTION_SAVE,
    ACTION_UPDATE,
    ACTION_DELETE,
    ACTION_LOAD,
    ACTION_RESET,
    ACTION_DISCARD
};

struct MenuItem {
    const char* text;
    MenuLevel nextLevel;
    MenuAction action;
    bool isEditable;
};

struct MenuState {
    MenuLevel currentLevel;
    int currentItem;
    int previousItem;
    int scrollPosition;
    bool isEditing;
    unsigned long lastActivity;
};

struct UserProfile {
    float pHMin, pHMax;
    int pumpSpeed, pumpTime;
    int FanSpeed, FanTime;
    float Volt, Current;
    int nameIndex;
    int typeIndex;
};

//==========================================================================
// Menu items definition
const MenuItem mainMenu[] = {
    {"Status", LEVEL_STATUS, ACTION_NONE, false},
    {"Start", LEVEL_START, ACTION_NONE, false},
    {"Setup", LEVEL_SETUP, ACTION_NONE, false}
};

const MenuItem setupMenu[] = {
    {"Config Profiles", LEVEL_CONFIG_PROFILES, ACTION_NONE, false},
    {"Reset to Default", risetDefault, ACTION_RESET, false},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
//sensor setting
const MenuItem sensorSettingsMenu[] = {
    {"pH Settings", LEVEL_EDIT_pH, ACTION_NONE, false},
    {"Temp Settings", LEVEL_EDIT_Temp, ACTION_NONE, false},
    {"Gas Settings", LEVEL_EDIT_Gas, ACTION_NONE, false},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem pHsettings[] = {
    {"Set Min: ", SetMinpH, ACTION_NONE, true},
    {"Set Max: ", SetMaxpH, ACTION_NONE, true},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem Tempsettings[] = {
    {"Set Min: ", SetMinTemp, ACTION_NONE, true},
    {"Set Max: ", SetMaxTemp, ACTION_NONE, true},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem Gassettings[] = {
    {"Set Min: ", SetMinGas, ACTION_NONE, true},
    {"Set Max: ", SetMaxGas, ACTION_NONE, true},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
//output setting
const MenuItem outputSettings[] = {
    {"Pump Setting ", PumpSetting, ACTION_NONE, false},
    {"Fan Setting ", FanSetting, ACTION_NONE, false},
    {"Voltage Control ", VoltageControl, ACTION_NONE, false},
    {"Current Control ", CurrentControl, ACTION_NONE, false},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem pumpSetting[] = {
    {"Speed (%): ", PumpSpeed, ACTION_NONE, true},
    {"Time (min): ", PumpTime, ACTION_NONE, true},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem fanSetting[] = {
    {"Speed (%): ", FanSpeed, ACTION_NONE, true},
    {"Time (min): ", FanTime, ACTION_NONE, true},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem voltControl[] = {
    {"Volt (V): ", voltSet, ACTION_NONE, true},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem curreControl[] = {
    {"Cur(mA): ", curSet, ACTION_NONE, true},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
//config profiles
const MenuItem configProfiles[] = {
    {"User 1: Gold A", userA, ACTION_NONE, false},
    {"Create New User ", NewUser, ACTION_NONE, false},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem newUser[] = {
    {"pH Min ", SetMinpH, ACTION_NONE, true},
    {"pH Max ", SetMaxpH, ACTION_NONE, true},
    {"Pump % ", PumpSpeed, ACTION_NONE, true},
    {"Pump (min) ", PumpTime, ACTION_NONE, true},
    {"Fan % ", FanSpeed, ACTION_NONE, true},
    {"Fan (min) ", FanTime, ACTION_NONE, true},
    {"Volt ", voltSet, ACTION_NONE, true},
    {"Current ", curSet, ACTION_NONE, true},
    {"Reset Profile ", resetProfile, ACTION_RESET, false},
    {"Save as Profile ", LEVEL_ADD_USER, ACTION_NONE, false},
    {"Back", LEVEL_CONFIG_PROFILES, ACTION_BACK, false}
};
const MenuItem UserA[] = {
    {"pH Min ", SetMinpH, ACTION_NONE, true},
    {"pH Max ", SetMaxpH, ACTION_NONE, true},
    {"Pump % ", PumpSpeed, ACTION_NONE, true},
    {"Pump (min) ", PumpTime, ACTION_NONE, true},
    {"Fan % ", FanSpeed, ACTION_NONE, true},
    {"Fan (min) ", FanTime, ACTION_NONE, true},
    {"Volt ", voltSet, ACTION_NONE, true},
    {"Current ", curSet, ACTION_NONE, true},
    {"Start Profile ", startProfile, ACTION_START, false},
    {"Reset Profile ", resetProfile, ACTION_RESET, false},
    {"Update Settings ", updateProfil, ACTION_UPDATE, false},
    {"Delete Profile ", deleteProfile, ACTION_DELETE, false},
    {"Back", LEVEL_SETUP, ACTION_BACK, false}
};
const MenuItem addUser[] = {
    {"User Name ", nameUser, ACTION_NONE, true},
    {"Profile Type ", typeUser, ACTION_NONE, true},
    {"Save User", SaveProfile, ACTION_SAVE, false},
    {"Back", LEVEL_ADD_USER, ACTION_BACK, false}
};

//Start Menu
const MenuItem startMenu[] = {
    {"Manual Mode ", LEVEL_MANUAL_SETTINGS, ACTION_NONE, false},
    {"Auto Mode ", LEVEL_chooseProfile, ACTION_NONE, false},
    {"Back", LEVEL_STATUS, ACTION_BACK, false},
};
//manual setting
const MenuItem manualSettingsMenu[] = {
    {"Sensor Settings", LEVEL_SENSOR_SETTINGS, ACTION_NONE, false},
    {"Output Settings", LEVEL_OUTPUT_SETTINGS, ACTION_NONE, false},
    {"Start Process", startProfile, ACTION_START, false},
    {"Back", LEVEL_START, ACTION_BACK, false}
};
//choose Profile
const MenuItem chooseProfile[] = {
    {"Back", LEVEL_START, ACTION_BACK, false}
};

#endif