# L298N Motor Driver - Detailed Wiring Guide

## Komponen yang Dibutuhkan

### Hardware
- 1x ESP32 Development Board
- 1x L298N Motor Driver Module
- 1x DC Motor untuk Pump (12V, 2-5A)
- 1x DC Motor untuk Fan (12V, 1-2A)
- 2x Relay Module 5V (Optional untuk safety)
- 1x Power Supply 12V (minimal 6A)
- 1x Power Supply 5V untuk ESP32
- Jumper wires
- Breadboard atau PCB
- Heatsink untuk L298N

### Tools
- Multimeter
- Screwdriver
- Wire strippers
- Soldering iron (jika perlu)

## Pin Mapping Detail

### ESP32 ke L298N (Updated Pin Mapping)
```
ESP32 GPIO    L298N Pin    Function            Wire Color (Suggested)
----------    ---------    --------            ------------------
GPIO5    ->   ENA          Water Pump Speed    Yellow
GPIO18   ->   IN1          Water Pump Dir 1    Orange
GPIO19   ->   IN2          Water Pump Dir 2    Red
GPIO14   ->   ENB          Fan Speed (PWM)     Green
GPIO27   ->   IN3          Fan Dir 1           Blue
GPIO26   ->   IN4          Fan Dir 2           Purple
5V       ->   VCC          Logic Power         Red
GND      ->   GND          Ground              Black
```

### Complete System Wiring
```
ESP32 GPIO    Component         Function
----------    ---------         --------
GPIO2    ->   Rotary Enc A      User interface
GPIO4    ->   Rotary Enc B      User interface
GPIO5    ->   L298N ENA         Water pump speed
GPIO12   ->   Temp Sensor       Temperature reading
GPIO13   ->   pH Sensor         pH measurement
GPIO14   ->   L298N ENB         Fan speed
GPIO15   ->   Encoder Button    User input
GPIO16   ->   XY6020 RX         Power supply control
GPIO17   ->   XY6020 TX         Power supply control
GPIO18   ->   L298N IN1         Water pump direction
GPIO19   ->   L298N IN2         Water pump direction
GPIO21   ->   LCD SDA           Display data
GPIO22   ->   LCD SCL           Display clock
GPIO26   ->   L298N IN4         Fan direction
GPIO27   ->   L298N IN3         Fan direction
GPIO39   ->   MQ Gas Sensor     Gas detection
```

### Power Connections
```
Power Source     L298N Pin    Function
------------     ---------    --------
12V PSU (+)  ->  12V          Motor Power
12V PSU (-)  ->  GND          Power Ground
5V PSU (+)   ->  VCC          Logic Power (atau dari ESP32)
5V PSU (-)   ->  GND          Logic Ground
```

### Motor Connections
```
Motor         L298N Output    Wire Color
-----         ------------    ----------
Pump (+)  ->  OUT1           Red
Pump (-)  ->  OUT2           Black
Fan (+)   ->  OUT3           Red
Fan (-)   ->  OUT4           Black
```

## Detailed Wiring Diagram

```
                    ESP32 DevKit (Updated Pin Mapping)
    ┌─────────────────────────────────────────────────────┐
    │                                                     │
    │  3V3  EN  VP  VN  D34 D35 D32 D33  D25 D26 D27 D14 │
    │                                                     │
    │  GND  23  22  1   3   21  19  18   5   17  16  4   │
    │                                                     │
    │  VIN  22  19  23  18  5   17  16   4   2   15  13  │
    │                                                     │
    │  GND  21  3   1   22  23  VIN  EN  39  0   2   15  │
    │                                                     │
    │  D15  D2  D4  RX2 TX2 D5  D18 D19  D13 D12 D14 D27 │
    │                                                     │
    │  D13  D12 D14 D27 D26 D25 D33 D32  D39 D0  D2  D15 │
    │                                                     │
    └─────────────────────────────────────────────────────┘
           │   │   │   │   │   │   │   │   │   │   │   │
           │   │   │   │   │   │   │   │   │   │   │   │
          D13 D12 D14 D27 D26  -   -   -  D39  -  D2  D15
           │   │   │   │   │               │       │   │
           │   │   │   │   │               │       │   │
           │   │   │   │   └─── Fan IN4    │       │   │
           │   │   │   └─────── Fan IN3    │       │   │
           │   │   └─────────── Fan EN     │       │   │
           │   └─────────────── Temp Sensor│       │   │
           └───────────────────── pH Sensor│       │   │
                                          │       │   │
                                    MQ Gas │       │   │
                                    Sensor │       │   │
                                          │       │   │
                                          │   Rotary   │
                                          │   Enc A    │
                                          │            │
                                          │        Encoder
                                          │        Button
                                          │
                                    Additional Pins:
                                    GPIO5  -> Pump EN
                                    GPIO18 -> Pump IN1
                                    GPIO19 -> Pump IN2
                                    GPIO21 -> LCD SDA
                                    GPIO22 -> LCD SCL
                                    GPIO16 -> XY6020 RX
                                    GPIO17 -> XY6020 TX
                                    GPIO4  -> Rotary Enc B

                        L298N Module
    ┌─────────────────────────────────────────────────┐
    │                                                 │
    │  [12V] [GND] [5V] [ENA] [IN1] [IN2] [ENB] [IN3] [IN4] │
    │    │     │    │     │     │     │     │     │     │  │
    │    │     │    │     │     │     │     │     │     │  │
    │    │     │    │     │     │     │     │     │     │  │
    │  ┌─┴─┐ ┌─┴─┐ ┌─┴─┐ ┌─┴─┐ ┌─┴─┐ ┌─┴─┐ ┌─┴─┐ ┌─┴─┐ ┌─┴─┐│
    │  │12V│ │GND│ │5V │ │ENA│ │IN1│ │IN2│ │ENB│ │IN3│ │IN4││
    │  └───┘ └───┘ └───┘ └───┘ └───┘ └───┘ └───┘ └───┘ └───┘│
    │                                                 │
    │  [OUT1] [OUT2]           [OUT3] [OUT4]         │
    │    │      │                │      │            │
    └────┼──────┼────────────────┼──────┼────────────┘
         │      │                │      │
         │      │                │      │
    ┌────┴──┐ ┌─┴────┐      ┌────┴──┐ ┌─┴────┐
    │ PUMP  │ │ PUMP │      │ FAN   │ │ FAN  │
    │ (+)   │ │ (-)  │      │ (+)   │ │ (-)  │
    └───────┘ └──────┘      └───────┘ └──────┘

                    Power Supply
    ┌─────────────────────────────────┐
    │        12V 6A PSU               │
    │                                 │
    │  [+12V] [GND] [+5V] [GND]      │
    │    │     │     │     │          │
    └────┼─────┼─────┼─────┼──────────┘
         │     │     │     │
         │     │     │     │
         │     │     │     └── ESP32 GND
         │     │     └──────── ESP32 VIN (5V)
         │     └────────────── L298N GND
         └──────────────────── L298N 12V
```

## Step-by-Step Wiring Instructions

### Step 1: Power Connections
1. **Connect 12V Power to L298N:**
   - 12V PSU (+) → L298N 12V terminal
   - 12V PSU (-) → L298N GND terminal

2. **Connect 5V Logic Power:**
   - ESP32 5V → L298N VCC terminal
   - ESP32 GND → L298N GND terminal

### Step 2: Control Signal Connections
1. **Pump Motor Control:**
   - ESP32 GPIO26 → L298N ENA (Speed control)
   - ESP32 GPIO27 → L298N IN1 (Direction 1)
   - ESP32 GPIO14 → L298N IN2 (Direction 2)

2. **Fan Motor Control:**
   - ESP32 GPIO25 → L298N ENB (Speed control)
   - ESP32 GPIO33 → L298N IN3 (Direction 1)
   - ESP32 GPIO32 → L298N IN4 (Direction 2)

### Step 3: Motor Connections
1. **Pump Motor:**
   - Pump (+) wire → L298N OUT1
   - Pump (-) wire → L298N OUT2

2. **Fan Motor:**
   - Fan (+) wire → L298N OUT3
   - Fan (-) wire → L298N OUT4

### Step 4: Optional Safety Relays
1. **Pump Relay:**
   - ESP32 GPIO13 → Relay IN
   - Relay COM → Pump (+)
   - Relay NO → L298N OUT1

2. **Fan Relay:**
   - ESP32 GPIO12 → Relay IN
   - Relay COM → Fan (+)
   - Relay NO → L298N OUT3

## Testing Procedure

### 1. Power Test
```cpp
void testPower() {
    Serial.println("Testing Power...");
    // Check if ESP32 boots properly
    // Check L298N LED indicators
}
```

### 2. Direction Test
```cpp
void testDirection() {
    Serial.println("Testing Motor Direction...");
    
    // Test Pump Forward
    digitalWrite(PUMP_MOTOR_IN1_PIN, HIGH);
    digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);
    ledcWrite(PWM_CHANNEL_PUMP, 128);  // 50% speed
    delay(2000);
    
    // Test Pump Reverse
    digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);
    digitalWrite(PUMP_MOTOR_IN2_PIN, HIGH);
    delay(2000);
    
    // Stop Pump
    digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);
    digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);
    ledcWrite(PWM_CHANNEL_PUMP, 0);
    
    // Repeat for Fan...
}
```

### 3. Speed Test
```cpp
void testSpeed() {
    Serial.println("Testing Motor Speed...");
    
    // Set direction
    digitalWrite(PUMP_MOTOR_IN1_PIN, HIGH);
    digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);
    
    // Test different speeds
    for (int speed = 0; speed <= 255; speed += 51) {
        Serial.printf("Speed: %d\n", speed);
        ledcWrite(PWM_CHANNEL_PUMP, speed);
        delay(1000);
    }
    
    // Stop
    ledcWrite(PWM_CHANNEL_PUMP, 0);
}
```

## Safety Checklist

- [ ] Power supply capacity adequate for motors
- [ ] All ground connections secure
- [ ] No short circuits in wiring
- [ ] L298N heatsink installed
- [ ] Motor current within L298N limits (2A per channel)
- [ ] Emergency stop function tested
- [ ] Relay connections secure (if used)
- [ ] Wire gauge appropriate for current
- [ ] Connections properly insulated

## Common Issues and Solutions

### Issue: Motor tidak berputar
- Check power supply voltage
- Verify wiring connections
- Test motor directly with battery
- Check L298N enable jumpers

### Issue: Motor berputar pelan
- Increase PWM value
- Check voltage drop under load
- Verify power supply current capacity

### Issue: ESP32 restart saat motor start
- Use separate power supplies
- Add decoupling capacitors
- Check ground connections

### Issue: L298N overheating
- Add heatsink
- Reduce motor load
- Check for short circuits
- Verify current limits
