# XY6020 Communication Troubleshooting Guide
## Debugging ESP32 ↔ XY6020 Modbus Communication

### 🔧 **<PERSON><PERSON><PERSON><PERSON> yang <PERSON> Dilakukan**

#### 1. **Koreksi Display Logic**
**MASALAH**: pH dan Temperature diambil dari XY6020 (salah!)
**SOLUSI**: 
- ✅ pH dan Temperature dari sensor terpisah (pH sensor GPIO13, Temp sensor GPIO12)
- ✅ **HANYA** Voltage dan Current dari XY6020

```cpp
// BENAR: pH/Temp dari sensor terpisah, V/I dari XY6020
lcd.print("pH:"); lcd.print(formatFloat(sensorData.pH, 1));        // Dari pH sensor
lcd.print("Temp:"); lcd.print(formatFloat(sensorData.temperature, 1)); // Dari temp sensor
lcd.print("Volt:"); lcd.print(formatFloat(sensorData.voltage, 1));     // Dari XY6020
lcd.print("Cur:"); lcd.print(formatFloat(sensorData.current, 2));      // Dari XY6020
```

#### 2. **Enhanced Debug Output**
**DITAMBAHKAN**: Comprehensive debugging untuk troubleshooting komunikasi

```cpp
// Debug initialization
void XY6020::begin() {
    Serial.println("=== XY6020 Initialization ===");
    Serial.printf("RX Pin: %d, TX Pin: %d, Slave Address: %d\n", rxPin, txPin, slaveAddress);
    // ... detailed initialization logging
}

// Debug communication attempts
void XY6020::task() {
    Serial.printf("XY6020: Attempting to read registers from slave %d...\n", slaveAddress);
    // ... detailed communication logging
}
```

#### 3. **Communication Test Function**
**DITAMBAHKAN**: `testXY6020Communication()` untuk systematic testing

### 🔍 **Troubleshooting Steps**

#### **Step 1: Check Serial Monitor Output**
Upload code dan monitor Serial output untuk melihat:

```
=== XY6020 Initialization ===
RX Pin: 16, TX Pin: 17, Slave Address: 1
Serial2 initialized: 115200 baud, 8N1
Modbus master initialized
XY6020 initialization complete on pins RX:16, TX:17, Slave:1

=== XY6020 Communication Test ===
Testing XY6020 communication...
```

#### **Step 2: Verify Hardware Connections**

**WIRING YANG BENAR**:
```
ESP32 GPIO16 (RX2) ──→ XY6020 TX
ESP32 GPIO17 (TX2) ──→ XY6020 RX  
ESP32 GND          ──→ XY6020 GND
```

**COMMON MISTAKES**:
- ❌ ESP32 TX → XY6020 TX (salah!)
- ❌ ESP32 RX → XY6020 RX (salah!)
- ✅ ESP32 TX → XY6020 RX (benar!)
- ✅ ESP32 RX → XY6020 TX (benar!)

#### **Step 3: Check XY6020 Settings**

**XY6020 Modbus Configuration**:
1. **Baud Rate**: 115200 (default biasanya 9600, perlu diubah!)
2. **Slave Address**: 1 (default)
3. **Data Format**: 8N1
4. **Parity**: None

**Cara Setting XY6020**:
1. Tekan tombol SET pada XY6020
2. Navigate ke Modbus settings
3. Set baud rate ke 115200
4. Set slave address ke 1
5. Save settings

#### **Step 4: Test dengan Multimeter**

**Check Power**:
- XY6020 power LED menyala
- Input voltage sesuai spesifikasi
- ESP32 mendapat power 3.3V/5V

**Check Signal**:
- Voltage pada TX/RX pins saat komunikasi
- Ground connection antara ESP32 dan XY6020

#### **Step 5: Software Debugging**

**Monitor Serial Output**:
```
XY6020: Attempting to read registers from slave 1...
XY6020: Read request queued successfully
XY6020: Read success - V:0.00 I:0.00 P:0.0 Out:0
```

**Jika melihat**:
- ✅ "Read success" → Komunikasi OK
- ❌ "Read failed" → Check wiring/settings
- ❌ "Failed to queue" → Check Modbus initialization

### 🛠️ **Common Issues & Solutions**

#### **Issue 1: "XY6020: Read failed - Error: 0xXX"**
**Possible Causes**:
- Wrong baud rate (XY6020 masih 9600, ESP32 115200)
- Wrong slave address
- Bad wiring
- XY6020 not powered

**Solutions**:
1. Check XY6020 baud rate setting
2. Try different slave addresses (1, 2, 3)
3. Verify wiring with multimeter
4. Check XY6020 power supply

#### **Issue 2: "Modbus busy, skipping read"**
**Cause**: Previous transaction not completed
**Solution**: Normal behavior, akan retry otomatis

#### **Issue 3: "Failed to queue read request"**
**Possible Causes**:
- Modbus not initialized properly
- Serial port conflict
- Hardware issue

**Solutions**:
1. Check Serial2 initialization
2. Ensure no other code uses Serial2
3. Try different ESP32 board

#### **Issue 4: Connection intermittent**
**Possible Causes**:
- Loose wiring
- Power supply noise
- EMI interference

**Solutions**:
1. Use twisted pair cables
2. Add ferrite cores
3. Separate power and signal grounds
4. Use shielded cables

### 📊 **Expected Serial Output**

#### **Successful Communication**:
```
=== XY6020 Initialization ===
RX Pin: 16, TX Pin: 17, Slave Address: 1
Serial2 initialized: 115200 baud, 8N1
Modbus master initialized
XY6020 initialization complete on pins RX:16, TX:17, Slave:1

XY6020: Attempting to read registers from slave 1...
XY6020: Read request queued successfully
XY6020: Read success - V:12.34 I:1.23 P:15.2 Out:1

XY6020 Status: CONN | V:12.34 | I:1.23 | P:15.2
```

#### **Failed Communication**:
```
XY6020: Attempting to read registers from slave 1...
XY6020: Read request queued successfully
XY6020: Read failed - Error: 0x04

XY6020 Status: DISC | V:0.00 | I:0.00 | P:0.0
```

### 🎯 **Quick Test Procedure**

1. **Upload code** dengan debug output
2. **Open Serial Monitor** (115200 baud)
3. **Check initialization** messages
4. **Verify wiring** jika ada error
5. **Check XY6020 settings** jika masih error
6. **Test dengan simple Modbus scanner** jika perlu

### 📝 **Debug Commands**

Tambahkan di main loop untuk manual testing:
```cpp
// Add to setup() for one-time test
testXY6020Communication();

// Add to loop() for periodic monitoring  
if (millis() - lastStatusLog >= 10000) {
    xy6020.printStatus();
}
```

Dengan perbaikan ini, Anda seharusnya bisa melihat detail komunikasi di Serial Monitor dan mengidentifikasi masalah komunikasi XY6020.
