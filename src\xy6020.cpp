#include "xy6020.h"

XY6020::XY6020(int rx_pin, int tx_pin, uint8_t slave_addr) 
    : rxPin(rx_pin), txPin(tx_pin), slaveAddress(slave_addr), 
      connectionStatus(false), lastReadTime(0) {
    
    // Initialize registers array
    for (int i = 0; i < 20; i++) {
        registers[i] = 0;
    }
    
    // Initialize serial
    serial = &Serial2;
}

void XY6020::begin() {
    Serial.println("Initializing XY6020...");
    
    // Initialize serial communication
    serial->begin(115200, SERIAL_8N1, rxPin, txPin);
    serial->setTimeout(100);
    
    // Initialize Modbus
    modbus.begin(serial);
    modbus.master();
    
    Serial.printf("XY6020 initialized on pins RX:%d, TX:%d, Slave:%d\n", 
                  rxPin, txPin, slaveAddress);
}

void XY6020::task() {
    static unsigned long lastConnectionAttempt = 0;
    unsigned long currentTime = millis();
    
    // Process Modbus tasks
    modbus.task();
    yield();
    
    // Read XY6020 registers every 500ms
    if (currentTime - lastReadTime >= 500) {
        lastReadTime = currentTime;
        
        if (!modbus.slave()) {
            // Read all registers from 0 to OUTPUT_STATE
            bool result = modbus.readHreg(slaveAddress, 0, registers, REG_OUTPUT_STATE + 1,
                std::bind(&XY6020::readCallback, this, 
                         std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
            
            if (!result) {
                Serial.println("XY6020: Failed to queue read request");
                connectionStatus = false;
            }
        }
    }
    
    // Try reconnection every 5 seconds if not connected
    if (!connectionStatus && (currentTime - lastConnectionAttempt > 5000)) {
        lastConnectionAttempt = currentTime;
        Serial.println("XY6020: Attempting to reconnect...");
        
        // Force a read attempt
        if (!modbus.slave()) {
            modbus.readHreg(slaveAddress, 0, registers, REG_OUTPUT_STATE + 1,
                std::bind(&XY6020::readCallback, this,
                         std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
        }
    }
}

bool XY6020::readCallback(Modbus::ResultCode event, uint16_t transactionId, void* data) {
    if (event == Modbus::EX_SUCCESS) {
        connectionStatus = true;
        Serial.printf("XY6020: Read success - V:%.2f I:%.2f P:%.1f Out:%d\n",
                     getActualVoltage(), getActualCurrent(), getActualPower(), getOutputState());
    } else {
        connectionStatus = false;
        Serial.printf("XY6020: Read failed - Error: 0x%02X\n", event);
    }
    
    yield();
    return true;
}

bool XY6020::writeCallback(Modbus::ResultCode event, uint16_t transactionId, void* data) {
    if (event == Modbus::EX_SUCCESS) {
        Serial.println("XY6020: Write success");
    } else {
        Serial.printf("XY6020: Write failed - Error: 0x%02X\n", event);
    }
    
    yield();
    return true;
}

void XY6020::waitForTransaction() {
    unsigned long startTime = millis();
    while (modbus.slave() && (millis() - startTime < 1000)) {
        modbus.task();
        delay(10);
        yield();
    }
}

bool XY6020::writeRegister(uint16_t reg, uint16_t value) {
    if (!connectionStatus) {
        Serial.println("XY6020: Not connected, cannot write");
        return false;
    }
    
    Serial.printf("XY6020: Writing register %d = %d\n", reg, value);
    
    waitForTransaction();
    
    bool result = modbus.writeHreg(slaveAddress, reg, value,
        std::bind(&XY6020::writeCallback, this,
                 std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
    
    if (result) {
        waitForTransaction();
        delay(100); // Give device time to process
    }
    
    return result;
}

bool XY6020::setTargetVoltage(float voltage) {
    voltage = constrain(voltage, 0.0, 60.0);
    uint16_t value = (uint16_t)(voltage * 100);
    
    if (writeRegister(REG_TARGET_VOLTAGE, value)) {
        Serial.printf("XY6020: Target voltage set to %.2fV\n", voltage);
        return true;
    }
    
    Serial.println("XY6020: Failed to set target voltage");
    return false;
}

bool XY6020::setMaxCurrent(float current) {
    current = constrain(current, 0.0, 20.0);
    uint16_t value = (uint16_t)(current * 100);
    
    if (writeRegister(REG_MAX_CURRENT, value)) {
        Serial.printf("XY6020: Max current set to %.2fA\n", current);
        return true;
    }
    
    Serial.println("XY6020: Failed to set max current");
    return false;
}

bool XY6020::setOutputEnabled(bool enabled) {
    uint16_t value = enabled ? 1 : 0;
    
    if (writeRegister(REG_OUTPUT_STATE, value)) {
        Serial.printf("XY6020: Output %s\n", enabled ? "enabled" : "disabled");
        return true;
    }
    
    Serial.printf("XY6020: Failed to %s output\n", enabled ? "enable" : "disable");
    return false;
}

void XY6020::printStatus() {
    Serial.println("=== XY6020 Status ===");
    Serial.printf("Connected: %s\n", connectionStatus ? "Yes" : "No");
    if (connectionStatus) {
        Serial.printf("Target: %.2fV / %.2fA\n", getTargetVoltage(), getMaxCurrent());
        Serial.printf("Actual: %.2fV / %.2fA / %.1fW\n", 
                     getActualVoltage(), getActualCurrent(), getActualPower());
        Serial.printf("Input: %.2fV\n", getInputVoltage());
        Serial.printf("Output: %s\n", getOutputState() ? "ON" : "OFF");
    }
    Serial.println("====================");
}
