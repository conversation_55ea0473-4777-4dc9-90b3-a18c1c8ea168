#include "xy6020.h"
#include "settings.h"

Xy6020::Xy6020(SettingsData &cfg, int8_t rx_pin, int8_t tx_pin,
               uint8_t slave_address)
#ifdef ESP32
    : mSlaveAddr(slave_address),
      mConnectionStatus(false), mCfg(cfg), mReadResultCounter(0) {
  for (int b = 0; b < READ_RESULTS_BUFFER_SIZE; ++b) {
    mReadResults[b] = false;
  }

  // Modbus init
  mSerial.begin(115200, SERIAL_8N1, rx_pin, tx_pin);
  mSerial.setTimeout(100);
  mModBus.begin(&mSerial);
#else
    : mSlaveAddr(slave_address), mSoftSerial(rx_pin, tx_pin),
      mConnectionStatus(false), mCfg(cfg), mReadResultCounter(0) {
  for (int b = 0; b < READ_RESULTS_BUFFER_SIZE; ++b) {
    mReadResults[b] = false;
  }

  // Modbus init
  mSoftSerial.begin(115200, SWSERIAL_8N1);
  mSoftSerial.setTimeout(100);
  mModBus.begin(&mSoftSerial);
#endif
  mModBus.master();
  mTs = millis();
}

bool Xy6020::readHregCb(Modbus::ResultCode event, uint16_t transactionId,
                        void *data) {
  mReadResults[mReadResultCounter % READ_RESULTS_BUFFER_SIZE] =
      (event == Modbus::EX_SUCCESS);
  mReadResultCounter++;

  mConnectionStatus = false;
  for (int b = 0; b < READ_RESULTS_BUFFER_SIZE; ++b) {
    if (mReadResults[b]) {
      mConnectionStatus = true;
      break;
    }
  }
#ifdef ESP32
  Serial.printf_P("HReg read: 0x%02X, Connected: %d\n", event, mConnectionStatus);

  // Print some register values for debugging
  if (event == Modbus::EX_SUCCESS) {
    Serial.printf_P("Voltage: %.2fV, Current: %.2fA, Power: %.1fW, Output: %d\n",
                   actualVoltage(), actualCurrent(), actualPower(), outputEnabled());
    Serial.printf_P("Input Voltage: %.2fV\n", inputVoltage());
  }
#else
  Serial.printf_P("HReg read: 0x%02X, Mem: %d\n", event, ESP.getFreeHeap());
#endif
  //  delay(50);
  yield();

  return true;
}

bool Xy6020::writeCb(Modbus::ResultCode event, uint16_t transactionId,
                     void *data) {
#ifdef ESP32
  Serial.printf_P("Request result: 0x%02X\n", event);
#else
  Serial.printf_P("Request result: 0x%02X, Mem: %d\n", event,
                  ESP.getFreeHeap());
#endif
  return true;
}

void Xy6020::task() {
  static unsigned long lastConnectionAttempt = 0;
  static bool wasConnected = false;

  // Process Modbus tasks
  mModBus.task();
  yield();

  // Poll XY6020L registers every 500ms
  if (millis() > mTs + 500) {
    mTs = millis();
    if (!mModBus.slave()) {
      bool result = mModBus.readHreg(mSlaveAddr, 0x0, mRegisters, OutputState + 1,
                       std::bind(&Xy6020::readHregCb, this,
                                 std::placeholders::_1, std::placeholders::_2,
                                 std::placeholders::_3));

      if (!result) {
        Serial.println("Error: Failed to queue Modbus read request");
      }
    }
  }

  // Connection status reporting
  if (wasConnected != mConnectionStatus) {
    wasConnected = mConnectionStatus;
    if (mConnectionStatus) {
      Serial.println("XY6020L connected successfully!");
    } else {
      Serial.println("XY6020L connection lost!");
    }
  }

  // If not connected, try more aggressive reconnection every 5 seconds
  if (!mConnectionStatus && (millis() - lastConnectionAttempt > 5000)) {
    lastConnectionAttempt = millis();
    Serial.println("Attempting to reconnect to XY6020L...");

    // Reset Modbus if needed
    if (mModBus.slave()) {
      Serial.println("Modbus is busy, resetting...");
      mModBus.task();
      yield();
    }

    // Force a read attempt
    bool result = mModBus.readHreg(mSlaveAddr, 0x0, mRegisters, OutputState + 1,
                     std::bind(&Xy6020::readHregCb, this,
                               std::placeholders::_1, std::placeholders::_2,
                               std::placeholders::_3));

    if (!result) {
      Serial.println("Error: Failed to queue reconnection read request");
    }
  }

  // Handle input voltage limits if enabled and connected
  if (mConnectionStatus && mCfg.enable_input_limits) {
    if (inputVoltage() < mCfg.switch_off_voltage &&
        mRegisters[OutputState] != 0) {
      Serial.println("Disable output on lower input level");
      setOutputEnabled(false);
    } else if (inputVoltage() > mCfg.switch_on_voltage &&
               mRegisters[OutputState] == 0) {
      Serial.println("Enable output on upper input level");
      setOutputEnabled(true);
    }
  }
}

bool Xy6020::setTargetVoltage(float voltage) {
  bool ret = true;
  if (voltage < 0) {
    voltage = 0;
    ret = false;
  } else if (voltage > 60) {
    voltage = 60;
    ret = false;
  }
  uint16_t value = voltage * 100;
  setRegister(TargetVoltage, value);
  return ret;
}

bool Xy6020::setMaxCurrent(float current) {
  bool ret = true;
  if (current < 0) {
    current = 0;
    ret = false;
  } else if (current > 20) {
    current = 20.0;
    ret = false;
  }
  uint16_t value = current * 100;
  setRegister(MaxCurrent, value);
  return ret;
}
bool Xy6020::setPower(float power) {
  auto max_voltage = targetVoltage();
  if (max_voltage <= 0) {
    return false;
  }
  if (power > mCfg.max_power) {
    power = mCfg.max_power;
  }
  auto target_current = power / max_voltage;
  if (target_current > 20) {
    target_current = 20;
  }
  setMaxCurrent(target_current);
  return true;
}
bool Xy6020::setMaxPower(float power) {
  if (power < 0) {
    return false;
  }
  bool need_correction = false;
  if (power < mCfg.max_power) {
    need_correction = true;
  }
  mCfg.max_power = power;
  if (need_correction) {
    return setPower(power);
  }
  return true;
}

void Xy6020::setOutputEnabled(bool state) { setRegister(OutputState, state); }

void Xy6020::waitForTransactionDone() {
  while (mModBus.slave()) { // Check if transaction is active
    mModBus.task();
    delay(10);
    yield();
  }
}

void Xy6020::setRegister(Register reg, uint16_t value) {
  Serial.printf_P("setRegister(%d, %d)\n", reg, value);

  // Check if we're connected before trying to write
  if (!mConnectionStatus) {
    Serial.println("Warning: Trying to set register but not connected to XY6020L");
  }

  waitForTransactionDone();
  bool result = mModBus.writeHreg(mSlaveAddr, reg, value,
                    std::bind(&Xy6020::writeCb, this, std::placeholders::_1,
                              std::placeholders::_2, std::placeholders::_3));

  if (!result) {
    Serial.println("Error: Failed to queue Modbus write request");
  }

  waitForTransactionDone();
  delay(100); // Increase delay to give more time for the device to respond
  yield();

  // Force a read after write to update our local registers
  mModBus.readHreg(mSlaveAddr, 0x0, mRegisters, OutputState + 1,
                  std::bind(&Xy6020::readHregCb, this,
                            std::placeholders::_1, std::placeholders::_2,
                            std::placeholders::_3));
}
