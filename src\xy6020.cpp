#include "xy6020.h"

XY6020::XY6020(int rx_pin, int tx_pin, uint8_t slave_addr) 
    : rxPin(rx_pin), txPin(tx_pin), slaveAddress(slave_addr), 
      connectionStatus(false), lastReadTime(0) {
    
    // Initialize registers array
    for (int i = 0; i < 20; i++) {
        registers[i] = 0;
    }
    
    // Initialize serial
    serial = &Serial2;
}

void XY6020::begin() {
    Serial.println("=== XY6020 Initialization ===");
    Serial.printf("RX Pin: %d, TX Pin: %d, Slave Address: %d\n", rxPin, txPin, slaveAddress);

    // Initialize serial communication
    serial->begin(115200, SERIAL_8N1, rxPin, txPin);
    serial->setTimeout(100);
    Serial.println("Serial2 initialized: 115200 baud, 8N1");

    // Initialize Modbus
    modbus.begin(serial);
    modbus.master();
    Serial.println("Modbus master initialized");

    Serial.printf("XY6020 initialization complete on pins RX:%d, TX:%d, Slave:%d\n",
                  rxPin, txPin, slaveAddress);
    Serial.println("==============================");
}

void XY6020::task() {
    static unsigned long lastConnectionAttempt = 0;
    unsigned long currentTime = millis();
    
    // Process Modbus tasks
    modbus.task();
    yield();
    
    // Read XY6020 registers every 500ms
    if (currentTime - lastReadTime >= 500) {
        lastReadTime = currentTime;

        if (!modbus.slave()) {
            Serial.printf("XY6020: Attempting to read registers from slave %d...\n", slaveAddress);
            // Read all registers from 0 to OUTPUT_STATE
            bool result = modbus.readHreg(slaveAddress, 0, registers, REG_OUTPUT_STATE + 1,
                std::bind(&XY6020::readCallback, this,
                         std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));

            if (!result) {
                Serial.println("XY6020: Failed to queue read request");
                connectionStatus = false;
            } else {
                Serial.println("XY6020: Read request queued successfully");
            }
        } else {
            Serial.println("XY6020: Modbus busy, skipping read");
        }
    }
    
    // Try reconnection every 5 seconds if not connected
    if (!connectionStatus && (currentTime - lastConnectionAttempt > 5000)) {
        lastConnectionAttempt = currentTime;
        Serial.println("XY6020: Attempting to reconnect...");
        
        // Force a read attempt
        if (!modbus.slave()) {
            modbus.readHreg(slaveAddress, 0, registers, REG_OUTPUT_STATE + 1,
                std::bind(&XY6020::readCallback, this,
                         std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
        }
    }
}

bool XY6020::readCallback(Modbus::ResultCode event, uint16_t transactionId, void* data) {
    if (event == Modbus::EX_SUCCESS) {
        connectionStatus = true;
        Serial.printf("XY6020: Read success - V:%.2f I:%.2f P:%.1f Out:%d\n",
                     getActualVoltage(), getActualCurrent(), getActualPower(), getOutputState());
    } else {
        connectionStatus = false;
        Serial.printf("XY6020: Read failed - Error: 0x%02X\n", event);
    }
    
    yield();
    return true;
}

bool XY6020::writeCallback(Modbus::ResultCode event, uint16_t transactionId, void* data) {
    if (event == Modbus::EX_SUCCESS) {
        Serial.println("XY6020: Write success");
    } else {
        Serial.printf("XY6020: Write failed - Error: 0x%02X\n", event);
    }
    
    yield();
    return true;
}

void XY6020::waitForTransaction() {
    unsigned long startTime = millis();
    while (modbus.slave() && (millis() - startTime < 1000)) {
        modbus.task();
        delay(10);
        yield();
    }
}

bool XY6020::writeRegister(uint16_t reg, uint16_t value) {
    if (!connectionStatus) {
        Serial.println("XY6020: Not connected, cannot write");
        return false;
    }
    
    Serial.printf("XY6020: Writing register %d = %d\n", reg, value);
    
    waitForTransaction();
    
    bool result = modbus.writeHreg(slaveAddress, reg, value,
        std::bind(&XY6020::writeCallback, this,
                 std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
    
    if (result) {
        waitForTransaction();
        delay(100); // Give device time to process
    }
    
    return result;
}

bool XY6020::setTargetVoltage(float voltage) {
    voltage = constrain(voltage, 0.0, 60.0);
    uint16_t value = (uint16_t)(voltage * 100);
    
    if (writeRegister(REG_TARGET_VOLTAGE, value)) {
        Serial.printf("XY6020: Target voltage set to %.2fV\n", voltage);
        return true;
    }
    
    Serial.println("XY6020: Failed to set target voltage");
    return false;
}

bool XY6020::setMaxCurrent(float current) {
    current = constrain(current, 0.0, 20.0);
    uint16_t value = (uint16_t)(current * 100);
    
    if (writeRegister(REG_MAX_CURRENT, value)) {
        Serial.printf("XY6020: Max current set to %.2fA\n", current);
        return true;
    }
    
    Serial.println("XY6020: Failed to set max current");
    return false;
}

bool XY6020::setOutputEnabled(bool enabled) {
    uint16_t value = enabled ? 1 : 0;
    
    if (writeRegister(REG_OUTPUT_STATE, value)) {
        Serial.printf("XY6020: Output %s\n", enabled ? "enabled" : "disabled");
        return true;
    }
    
    Serial.printf("XY6020: Failed to %s output\n", enabled ? "enable" : "disable");
    return false;
}

void XY6020::printStatus() {
    Serial.println("=== XY6020 Detailed Status ===");
    Serial.printf("Connection Status: %s\n", connectionStatus ? "CONNECTED" : "DISCONNECTED");
    Serial.printf("Communication Pins: RX=%d, TX=%d\n", rxPin, txPin);
    Serial.printf("Slave Address: %d\n", slaveAddress);
    Serial.printf("Last Read Time: %lu ms ago\n", millis() - lastReadTime);

    if (connectionStatus) {
        Serial.println("--- Register Values ---");
        Serial.printf("Target Voltage: %.2fV (reg=%d)\n", getTargetVoltage(), registers[REG_TARGET_VOLTAGE]);
        Serial.printf("Max Current: %.2fA (reg=%d)\n", getMaxCurrent(), registers[REG_MAX_CURRENT]);
        Serial.printf("Actual Voltage: %.2fV (reg=%d)\n", getActualVoltage(), registers[REG_ACTUAL_VOLTAGE]);
        Serial.printf("Actual Current: %.2fA (reg=%d)\n", getActualCurrent(), registers[REG_ACTUAL_CURRENT]);
        Serial.printf("Actual Power: %.1fW (reg=%d)\n", getActualPower(), registers[REG_ACTUAL_POWER]);
        Serial.printf("Input Voltage: %.2fV (reg=%d)\n", getInputVoltage(), registers[REG_INPUT_VOLTAGE]);
        Serial.printf("Output State: %s (reg=%d)\n", getOutputState() ? "ON" : "OFF", registers[REG_OUTPUT_STATE]);
    } else {
        Serial.println("No data available - device not connected");
        Serial.println("Check:");
        Serial.println("1. Wiring: ESP32 GPIO16->XY6020 TX, ESP32 GPIO17->XY6020 RX");
        Serial.println("2. Power: XY6020 powered on");
        Serial.println("3. Baud rate: XY6020 set to 115200");
        Serial.println("4. Slave address: XY6020 set to address 1");
    }
    Serial.println("==============================");
}
