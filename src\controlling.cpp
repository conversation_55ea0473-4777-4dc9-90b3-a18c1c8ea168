#include "controlling.h"
#include "xy6020.h"
#include "sensor.h"

// Global variables
SystemStatus systemStatus = {false, false, false, false, 0, 0, 0};
SensorData sensorData = {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0};
ControlSettings controlSettings = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0.0, 0.0};

// XY6020 instance
XY6020 xy6020(XY6020_RX_PIN, XY6020_TX_PIN, 1);

// Timing constants
const unsigned long SENSOR_READ_INTERVAL = 1000;    // 1 second
const unsigned long CONTROL_UPDATE_INTERVAL = 500;  // 0.5 second
const unsigned long XY6020_READ_INTERVAL = 500;     // 0.5 second

void initializeControlling() {
    Serial.println("Initializing Controlling System...");

    // Initialize sensors (now in separate sensor.cpp)
    initializeSensors();

    // Initialize actuators (motors)
    initializeActuators();

    // Initialize XY6020 power supply
    initializeXY6020();

    systemStatus.lastSensorRead = millis();
    systemStatus.lastControlUpdate = millis();

    Serial.println("Controlling System Initialized");
}

void controllingTask() {
    unsigned long currentTime = millis();
    
    // Read sensors periodically (now handled by sensor.cpp)
    if (currentTime - systemStatus.lastSensorRead >= SENSOR_READ_INTERVAL) {
        updateSensorReadings();

        // Update sensorData from sensor readings
        sensorData.pH = sensorReadings.pH;
        sensorData.temperature = sensorReadings.temperature;
        sensorData.gasLevel = sensorReadings.gasLevel;
        sensorData.lastUpdate = currentTime;

        // Update XY6020 sensor data
        updateXY6020Sensors();

        systemStatus.lastSensorRead = currentTime;
    }
    
    // Update XY6020 communication
    xy6020Task();
    
    // Update process control if running
    if (systemStatus.isRunning) {
        if (currentTime - systemStatus.lastControlUpdate >= CONTROL_UPDATE_INTERVAL) {
            updateProcessControl();
            systemStatus.lastControlUpdate = currentTime;
        }
        
        // Check safety limits
        if (!checkSafetyLimits()) {
            handleEmergencyStop();
        }
    }
}

// ========== Sensor Functions (moved to sensor.cpp) ==========
// Sensor functions are now handled by sensor.cpp
// This section is kept for XY6020 sensor integration

void updateXY6020Sensors() {
    // Update voltage, current, power from XY6020
    if (xy6020.isConnected()) {
        sensorData.voltage = xy6020.getActualVoltage();
        sensorData.current = xy6020.getActualCurrent();
        sensorData.power = xy6020.getActualPower();
        systemStatus.xy6020Connected = true;
    } else {
        systemStatus.xy6020Connected = false;
    }
}

// ========== Actuator Functions ==========
void initializeActuators() {
    Serial.println("Initializing Motor Drivers...");

    // Setup PWM channels for motor enable pins
    ledcSetup(PWM_CHANNEL_PUMP, PWM_FREQUENCY, PWM_RESOLUTION);
    ledcSetup(PWM_CHANNEL_FAN, PWM_FREQUENCY, PWM_RESOLUTION);

    // Attach PWM to motor enable pins
    ledcAttachPin(PUMP_MOTOR_EN_PIN, PWM_CHANNEL_PUMP);  // GPIO5
    ledcAttachPin(FAN_MOTOR_EN_PIN, PWM_CHANNEL_FAN);    // GPIO14

    // Setup direction control pins as outputs
    pinMode(PUMP_MOTOR_IN1_PIN, OUTPUT);  // GPIO18
    pinMode(PUMP_MOTOR_IN2_PIN, OUTPUT);  // GPIO19
    pinMode(FAN_MOTOR_IN1_PIN, OUTPUT);   // GPIO27
    pinMode(FAN_MOTOR_IN2_PIN, OUTPUT);   // GPIO26

    // Initialize all motors stopped
    setPumpSpeed(0);
    setFanSpeed(0);

    Serial.println("Motor Drivers Initialized:");
    Serial.printf("  Water Pump: EN=GPIO%d, IN1=GPIO%d, IN2=GPIO%d\n",
                  PUMP_MOTOR_EN_PIN, PUMP_MOTOR_IN1_PIN, PUMP_MOTOR_IN2_PIN);
    Serial.printf("  Fan Motor: EN=GPIO%d, IN1=GPIO%d, IN2=GPIO%d\n",
                  FAN_MOTOR_EN_PIN, FAN_MOTOR_IN1_PIN, FAN_MOTOR_IN2_PIN);
}

void setPumpSpeed(int speed) {
    speed = constrain(speed, 0, 100);
    int pwmValue = map(speed, 0, 100, 0, 255);

    if (speed > 0) {
        // Set motor direction (forward)
        digitalWrite(PUMP_MOTOR_IN1_PIN, HIGH);  // GPIO18
        digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);   // GPIO19

        // Set motor speed via PWM on enable pin
        ledcWrite(PWM_CHANNEL_PUMP, pwmValue);   // GPIO5

        systemStatus.pumpActive = true;
        Serial.printf("Water Pump ON - Speed: %d%% (PWM: %d)\n", speed, pwmValue);
    } else {
        // Stop motor
        digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);   // GPIO18
        digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);   // GPIO19
        ledcWrite(PWM_CHANNEL_PUMP, 0);          // GPIO5

        systemStatus.pumpActive = false;
        Serial.println("Water Pump OFF");
    }
}

void setFanSpeed(int speed) {
    speed = constrain(speed, 0, 100);
    int pwmValue = map(speed, 0, 100, 0, 255);

    if (speed > 0) {
        // Set motor direction (forward)
        digitalWrite(FAN_MOTOR_IN1_PIN, HIGH);   // GPIO27
        digitalWrite(FAN_MOTOR_IN2_PIN, LOW);    // GPIO26

        // Set motor speed via PWM on enable pin
        ledcWrite(PWM_CHANNEL_FAN, pwmValue);    // GPIO14

        systemStatus.fanActive = true;
        Serial.printf("Fan ON - Speed: %d%% (PWM: %d)\n", speed, pwmValue);
    } else {
        // Stop motor
        digitalWrite(FAN_MOTOR_IN1_PIN, LOW);    // GPIO27
        digitalWrite(FAN_MOTOR_IN2_PIN, LOW);    // GPIO26
        ledcWrite(PWM_CHANNEL_FAN, 0);           // GPIO14

        systemStatus.fanActive = false;
        Serial.println("Fan OFF");
    }
}

void stopAllActuators() {
    setPumpSpeed(0);
    setFanSpeed(0);
    setXY6020Output(false);

    // Ensure all motor pins are LOW
    digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);   // GPIO18
    digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);   // GPIO19
    digitalWrite(FAN_MOTOR_IN1_PIN, LOW);    // GPIO27
    digitalWrite(FAN_MOTOR_IN2_PIN, LOW);    // GPIO26

    // Set PWM to 0
    ledcWrite(PWM_CHANNEL_PUMP, 0);          // GPIO5
    ledcWrite(PWM_CHANNEL_FAN, 0);           // GPIO14

    Serial.println("All Actuators and Motors Stopped");
}

void setPumpDirection(bool forward) {
    if (forward) {
        digitalWrite(PUMP_MOTOR_IN1_PIN, HIGH);
        digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);
        Serial.println("Pump Direction: Forward");
    } else {
        digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);
        digitalWrite(PUMP_MOTOR_IN2_PIN, HIGH);
        Serial.println("Pump Direction: Reverse");
    }
}

void setFanDirection(bool forward) {
    if (forward) {
        digitalWrite(FAN_MOTOR_IN1_PIN, HIGH);
        digitalWrite(FAN_MOTOR_IN2_PIN, LOW);
        Serial.println("Fan Direction: Forward");
    } else {
        digitalWrite(FAN_MOTOR_IN1_PIN, LOW);
        digitalWrite(FAN_MOTOR_IN2_PIN, HIGH);
        Serial.println("Fan Direction: Reverse");
    }
}

void emergencyStopMotors() {
    Serial.println("EMERGENCY MOTOR STOP!");

    // Immediately stop all motors
    digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);   // GPIO18
    digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);   // GPIO19
    digitalWrite(FAN_MOTOR_IN1_PIN, LOW);    // GPIO27
    digitalWrite(FAN_MOTOR_IN2_PIN, LOW);    // GPIO26

    // Set PWM to 0
    ledcWrite(PWM_CHANNEL_PUMP, 0);          // GPIO5
    ledcWrite(PWM_CHANNEL_FAN, 0);           // GPIO14

    // Update status
    systemStatus.pumpActive = false;
    systemStatus.fanActive = false;
}

// ========== XY6020 Functions ==========
void initializeXY6020() {
    xy6020.begin();
}

void xy6020Task() {
    xy6020.task();
}

bool setXY6020Voltage(float voltage) {
    bool result = xy6020.setTargetVoltage(voltage);
    if (result) {
        controlSettings.targetVoltage = voltage;
    }
    return result;
}

bool setXY6020Current(float current) {
    bool result = xy6020.setMaxCurrent(current);
    if (result) {
        controlSettings.targetCurrent = current;
    }
    return result;
}

bool setXY6020Output(bool enabled) {
    return xy6020.setOutputEnabled(enabled);
}

float getXY6020Voltage() {
    return xy6020.getActualVoltage();
}

float getXY6020Current() {
    return xy6020.getActualCurrent();
}

float getXY6020Power() {
    return xy6020.getActualPower();
}

bool isXY6020Connected() {
    return xy6020.isConnected();
}

// ========== Process Control Functions ==========
void startElectrowiningProcess() {
    Serial.println("Starting Electrowining Process...");

    systemStatus.isRunning = true;
    systemStatus.processStartTime = millis();

    // Apply current settings
    setPumpSpeed(controlSettings.pumpSpeed);
    setFanSpeed(controlSettings.fanSpeed);
    setXY6020Voltage(controlSettings.targetVoltage);
    setXY6020Current(controlSettings.targetCurrent);
    setXY6020Output(true);

    Serial.println("Electrowining Process Started");
}

void stopElectrowiningProcess() {
    Serial.println("Stopping Electrowining Process...");

    systemStatus.isRunning = false;
    stopAllActuators();

    Serial.println("Electrowining Process Stopped");
}

void updateProcessControl() {
    // Update pump and fan based on time settings
    unsigned long processTime = (millis() - systemStatus.processStartTime) / 60000; // minutes

    // Pump control based on time
    if (controlSettings.pumpTime > 0) {
        if (processTime % (controlSettings.pumpTime * 2) < controlSettings.pumpTime) {
            setPumpSpeed(controlSettings.pumpSpeed);
        } else {
            setPumpSpeed(0);
        }
    }

    // Fan control based on time
    if (controlSettings.fanTime > 0) {
        if (processTime % (controlSettings.fanTime * 2) < controlSettings.fanTime) {
            setFanSpeed(controlSettings.fanSpeed);
        } else {
            setFanSpeed(0);
        }
    }

    // Update XY6020 settings if needed
    if (xy6020.isConnected()) {
        // Maintain voltage and current settings
        setXY6020Voltage(controlSettings.targetVoltage);
        setXY6020Current(controlSettings.targetCurrent);
    }
}

bool checkSafetyLimits() {
    // Check pH limits
    if (controlSettings.pHMin > 0 && sensorData.pH < controlSettings.pHMin) {
        Serial.println("Safety Alert: pH below minimum limit");
        return false;
    }
    if (controlSettings.pHMax > 0 && sensorData.pH > controlSettings.pHMax) {
        Serial.println("Safety Alert: pH above maximum limit");
        return false;
    }

    // Check temperature limits
    if (controlSettings.tempMin > 0 && sensorData.temperature < controlSettings.tempMin) {
        Serial.println("Safety Alert: Temperature below minimum limit");
        return false;
    }
    if (controlSettings.tempMax > 0 && sensorData.temperature > controlSettings.tempMax) {
        Serial.println("Safety Alert: Temperature above maximum limit");
        return false;
    }

    // Check power limits
    if (sensorData.power > 1200) { // 1200W max power
        Serial.println("Safety Alert: Power exceeds maximum limit");
        return false;
    }

    return true;
}

// ========== Profile Management Functions ==========
void loadProfileSettings(const UserProfile& profile) {
    controlSettings.pHMin = profile.pHMin;
    controlSettings.pHMax = profile.pHMax;
    controlSettings.pumpSpeed = profile.pumpSpeed;
    controlSettings.pumpTime = profile.pumpTime;
    controlSettings.fanSpeed = profile.FanSpeed;
    controlSettings.fanTime = profile.FanTime;
    controlSettings.targetVoltage = profile.Volt;
    controlSettings.targetCurrent = profile.Current;

    Serial.println("Profile settings loaded");
}

void applyManualSettings() {
    // Apply current manual settings to actuators
    if (systemStatus.isRunning) {
        setPumpSpeed(controlSettings.pumpSpeed);
        setFanSpeed(controlSettings.fanSpeed);
        setXY6020Voltage(controlSettings.targetVoltage);
        setXY6020Current(controlSettings.targetCurrent);
    }

    Serial.println("Manual settings applied");
}

// ========== Utility Functions ==========
void logSystemStatus() {
    Serial.println("=== System Status ===");
    Serial.printf("Running: %s\n", systemStatus.isRunning ? "Yes" : "No");
    Serial.printf("Pump: %s, Fan: %s\n",
                  systemStatus.pumpActive ? "Active" : "Inactive",
                  systemStatus.fanActive ? "Active" : "Inactive");
    Serial.printf("XY6020: %s\n", systemStatus.xy6020Connected ? "Connected" : "Disconnected");
    Serial.printf("pH: %.2f, Temp: %.2f°C\n", sensorData.pH, sensorData.temperature);
    Serial.printf("Voltage: %.2fV, Current: %.2fA, Power: %.1fW\n",
                  sensorData.voltage, sensorData.current, sensorData.power);
    Serial.println("====================");
}

void testXY6020Communication() {
    Serial.println("\n=== XY6020 Communication Test ===");
    Serial.println("Testing XY6020 communication...");

    // Print detailed status
    xy6020.printStatus();

    // Test basic communication
    if (xy6020.isConnected()) {
        Serial.println("✅ XY6020 is connected!");
        Serial.println("Testing control functions...");

        // Test voltage setting
        Serial.println("Setting voltage to 5.0V...");
        if (setXY6020Voltage(5.0)) {
            Serial.println("✅ Voltage set successfully");
        } else {
            Serial.println("❌ Failed to set voltage");
        }

        // Test current setting
        Serial.println("Setting current to 1.0A...");
        if (setXY6020Current(1.0)) {
            Serial.println("✅ Current set successfully");
        } else {
            Serial.println("❌ Failed to set current");
        }

    } else {
        Serial.println("❌ XY6020 is NOT connected!");
        Serial.println("\nTroubleshooting steps:");
        Serial.println("1. Check wiring:");
        Serial.println("   ESP32 GPIO16 -> XY6020 TX");
        Serial.println("   ESP32 GPIO17 -> XY6020 RX");
        Serial.println("   ESP32 GND   -> XY6020 GND");
        Serial.println("2. Check XY6020 power supply");
        Serial.println("3. Check XY6020 Modbus settings:");
        Serial.println("   - Baud rate: 115200");
        Serial.println("   - Slave address: 1");
        Serial.println("   - Data format: 8N1");
    }
    Serial.println("================================\n");
}

void handleEmergencyStop() {
    Serial.println("EMERGENCY STOP ACTIVATED!");

    stopElectrowiningProcess();

    // Log the reason for emergency stop
    logSystemStatus();
}
