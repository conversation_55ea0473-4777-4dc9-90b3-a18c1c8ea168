import os
import glob
import re
import requests

content_folder = "../content/" # input folder
result_file = "../content/result.h" # output file
split_lines = False # split output into multiple lines

def process_text(text):
    output = "\""

    for s in text:
        if s == '\f':
            output += "\\f"
        elif s == '\n':
            if split_lines:
                output += "\\n\"\n\""
            else:
                output += "\\n"
        elif s == '\r':
            output += "\\r"
        elif s == '\t':
            output += "\\t"
        elif s == '\"':
            output += "\\\""
        elif s == '\\':
            output += "\\\\"
        else:
            output += s

    output += "\""

    return output

def minify_html_by_api(html):
    try:
        payload = {'input': html}
        url = "https://www.toptal.com/developers/html-minifier/api/raw"
        print("Requesting HTML formatting...")
        r = requests.post(url, payload)
        if r.status_code == 200:
            return r.text
        else:
            print(f"API error: {r.status_code}")
            return simple_minify_html(html)
    except Exception as e:
        print(f"Error using API: {e}")
        return simple_minify_html(html)

def minify_css_by_api(css):
    try:
        payload = {'input': css}
        url = "https://www.toptal.com/developers/cssminifier/api/raw"
        print("Requesting CSS formatting...")
        r = requests.post(url, payload)
        if r.status_code == 200:
            return r.text
        else:
            print(f"API error: {r.status_code}")
            return simple_minify_css(css)
    except Exception as e:
        print(f"Error using API: {e}")
        return simple_minify_css(css)

def minify_js_by_api(js):
    try:
        payload = {'input': js}
        url = "https://www.toptal.com/developers/javascript-minifier/api/raw"
        print("Requesting JS formatting...")
        r = requests.post(url, payload)
        if r.status_code == 200:
            return r.text
        else:
            print(f"API error: {r.status_code}")
            return simple_minify_js(js)
    except Exception as e:
        print(f"Error using API: {e}")
        return simple_minify_js(js)

def simple_minify_html(html):
    # Simple HTML minification
    html = re.sub(r'\s{2,}', ' ', html)
    html = re.sub(r'>\s+<', '><', html)
    html = re.sub(r'<!--(.*?)-->', '', html, flags=re.DOTALL)
    return html

def simple_minify_css(css):
    # Simple CSS minification
    css = re.sub(r'/\*.*?\*/', '', css, flags=re.DOTALL)
    css = re.sub(r'\s{2,}', ' ', css)
    css = re.sub(r'\s*{\s*', '{', css)
    css = re.sub(r'\s*}\s*', '}', css)
    css = re.sub(r'\s*:\s*', ':', css)
    css = re.sub(r'\s*;\s*', ';', css)
    css = re.sub(r'\s*,\s*', ',', css)
    return css

def simple_minify_js(js):
    # Simple JS minification (very basic)
    js = re.sub(r'//.*?\n', '\n', js)
    js = re.sub(r'/\*.*?\*/', '', js, flags=re.DOTALL)
    js = re.sub(r'\s{2,}', ' ', js)
    return js

# For the given path, get the List of all files in the directory tree
def getListOfFiles(dirName):
    # create a list of file and sub directories
    # names in the given directory
    listOfFile = os.listdir(dirName)
    allFiles = list()
    # Iterate over all the entries
    for entry in listOfFile:
        # Create full path
        fullPath = os.path.join(dirName, entry)
        # If entry is a directory then get the list of files in this directory
        if os.path.isdir(fullPath):
            allFiles = allFiles + getListOfFiles(fullPath)
        else:
            allFiles.append(fullPath)

    return allFiles

# Get files
files_list_all = getListOfFiles(content_folder)
print(" --- ")

# Clear file
f = open(result_file, "w")
f.write("#ifndef RESULT_H\n#define RESULT_H\n\n")
f.close()

# Statistics
total_before_by_type = { }
total_after_by_type = { }

# Supported file types
text_file_types = ["html", "css", "js", "svg"]

# Process files
for i in files_list_all:
    print("File: " + i)
    file_type = i.split(".")[-1]

    if file_type in text_file_types:
        printed_file_name = file_type + "__" + i.replace(content_folder, "").replace("." + file_type, "").replace(".", "_").replace("\\", "_").replace("/", "_").replace("-", "")

        # Read original file
        f = open(i, "r", encoding="utf-8")
        text = f.read()
        f.close()

        # Save length before processing
        len_before = len(text)

        if file_type == "html":
            text = minify_html_by_api(text)
        elif file_type == "css":
            text = minify_css_by_api(text)
        elif file_type == "js":
            text = minify_js_by_api(text)
        elif file_type == "svg":
            text = re.sub("<!--(.*?)-->", "", text)
        else:
            print("Not supported type")

        len_after = len(text)

        total_after_by_type[file_type] = total_after_by_type.get(file_type, 0) + len_after
        total_before_by_type[file_type] = total_before_by_type.get(file_type, 0) + len_before

        text = process_text(text)

        print("Text size {0} / {1}".format(len_after, len_before))
        print(" --- ")

        # Write to result file
        f = open(result_file, "a")
        f.write("// Length {0} / {1}\n".format(len_after, len_before))
        f.write("const char PROGMEM " + printed_file_name + "[] = "+text+";\n\n")
        f.close()
    else:
        print("Not a text file")

# Close the header file
f = open(result_file, "a")
f.write("#endif // RESULT_H\n")
f.close()

total_before = 0
total_after = 0

print()
print("Saved space by file type: ")

for file_type in total_before_by_type:
    bv = total_before_by_type[file_type]
    av = total_after_by_type[file_type]
    print(" {0}: {1} / {2} ({3})".format(file_type, av, bv, (av-bv)))
    total_before += bv
    total_after += av
print()

print("Total: ")
print(" {0} / {1} ({2})".format(total_after, total_before, (total_after-total_before)))

print()
print(" --- ")
