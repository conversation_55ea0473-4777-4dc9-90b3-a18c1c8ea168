#include "display.h"
#include "controlling.h"
#include "sensor.h"

// Implementasi fungsi display
String formatFloat(float value, int decimalPlaces) {
    String formatted = String(value, decimalPlaces);
    return formatted;
}

void displayMainScreen() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("pH:"); lcd.print(pH); lcd.print("   Temp:"); lcd.print(temp); lcd.print("C");
    lcd.setCursor(0, 1);
    lcd.print("Pump:"); lcd.print(pumpSpeed); lcd.print("%   Fan:"); lcd.print(fanSpeed); lcd.print("%");
    lcd.setCursor(0, 2);
    lcd.print("Volt:"); lcd.print(voltage); lcd.print("V   Cur:"); lcd.print(formatFloat(current, 1)); lcd.print("mA");
    lcd.setCursor(0, 3);
    lcd.print(menuState.currentItem == 1 ? (isRunning ? " -> Stop" : " -> Start") : (isRunning ? "   Stop" : "   Start"));
    lcd.print(menuState.currentItem == 2 ? " -> Setup" : "   Setup");
}

void displaySetupMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Setup Menu");
    int menuSize = sizeof(setupMenu) / sizeof(setupMenu[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(setupMenu[itemIndex].text);
        }
    }
}

void displayManualSettingsMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Manual Settings");
    int menuSize = sizeof(manualSettingsMenu) / sizeof(manualSettingsMenu[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(manualSettingsMenu[itemIndex].text);
        }
    }
}

void displaySensorSettingsMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Sensor Settings");
    int menuSize = sizeof(sensorSettingsMenu) / sizeof(sensorSettingsMenu[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(sensorSettingsMenu[itemIndex].text);
        }
    }
}

void displayOutputSettingsMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Output Settings");
    int menuSize = sizeof(outputSettings) / sizeof(outputSettings[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(outputSettings[itemIndex].text);
        }
    }
}

void displayConfigProfilesMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Config Profiles");
    int menuSize = profileCount + 2;

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            if (itemIndex < profileCount) {
                lcd.print("User ");
                lcd.print(profiles[itemIndex].nameIndex);
                lcd.print(": ");
                lcd.print(profileTypes[profiles[itemIndex].typeIndex]);
            } else if (itemIndex == profileCount) {
                lcd.print("Create New User");
            } else {
                lcd.print("Back");
            }
        }
    }
}

void displayUserAMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("User ");
    lcd.print(profiles[currentProfileIndex].nameIndex);
    lcd.print(": ");
    lcd.print(profileTypes[profiles[currentProfileIndex].typeIndex]);
    int menuSize = sizeof(UserA) / sizeof(UserA[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            }
            lcd.print(UserA[itemIndex].text);
            if (itemIndex == 0) lcd.print(formatFloat(profiles[currentProfileIndex].pHMin, 1));
            else if (itemIndex == 1) lcd.print(formatFloat(profiles[currentProfileIndex].pHMax, 1));
            else if (itemIndex == 2) lcd.print(profiles[currentProfileIndex].pumpSpeed);
            else if (itemIndex == 3) lcd.print(profiles[currentProfileIndex].pumpTime);
            else if (itemIndex == 4) lcd.print(profiles[currentProfileIndex].FanSpeed);
            else if (itemIndex == 5) lcd.print(profiles[currentProfileIndex].FanTime);
            else if (itemIndex == 6) lcd.print(formatFloat(profiles[currentProfileIndex].Volt, 1));
            else if (itemIndex == 7) lcd.print(formatFloat(profiles[currentProfileIndex].Current, 1));
        }
    }
}

void displayNewUserMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("New User Settings");
    int menuSize = sizeof(newUser) / sizeof(newUser[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            }
            lcd.print(newUser[itemIndex].text);
            if (itemIndex == 0) lcd.print(formatFloat(tempProfile.pHMin, 1));
            else if (itemIndex == 1) lcd.print(formatFloat(tempProfile.pHMax, 1));
            else if (itemIndex == 2) lcd.print(tempProfile.pumpSpeed);
            else if (itemIndex == 3) lcd.print(tempProfile.pumpTime);
            else if (itemIndex == 4) lcd.print(tempProfile.FanSpeed);
            else if (itemIndex == 5) lcd.print(tempProfile.FanTime);
            else if (itemIndex == 6) lcd.print(formatFloat(tempProfile.Volt, 1));
            else if (itemIndex == 7) lcd.print(formatFloat(tempProfile.Current, 1));
        }
    }
}

void displayPumpSettingMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Pump Setting");
    int menuSize = sizeof(pumpSetting) / sizeof(pumpSetting[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Speed (%): "); lcd.print(controlSettings.pumpSpeed);
                } else if (itemIndex == 1) {
                    lcd.print("Time (min): "); lcd.print(controlSettings.pumpTime);
                } else {
                    lcd.print(pumpSetting[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(pumpSetting[itemIndex].text);
                if (itemIndex == 0) lcd.print(controlSettings.pumpSpeed);
                else if (itemIndex == 1) lcd.print(controlSettings.pumpTime);
            }
        }
    }
}

void displayFanSettingMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Fan Setting");
    int menuSize = sizeof(fanSetting) / sizeof(fanSetting[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Speed (%): "); lcd.print(controlSettings.fanSpeed);
                } else if (itemIndex == 1) {
                    lcd.print("Time (min): "); lcd.print(controlSettings.fanTime);
                } else {
                    lcd.print(fanSetting[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(fanSetting[itemIndex].text);
                if (itemIndex == 0) lcd.print(controlSettings.fanSpeed);
                else if (itemIndex == 1) lcd.print(controlSettings.fanTime);
            }
        }
    }
}

void displayVoltageControlMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Voltage Control");
    int menuSize = sizeof(voltControl) / sizeof(voltControl[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Volt (V): "); lcd.print(formatFloat(controlSettings.targetVoltage, 1));
                } else {
                    lcd.print(voltControl[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(voltControl[itemIndex].text);
                if (itemIndex == 0) lcd.print(formatFloat(controlSettings.targetVoltage, 1));
            }
        }
    }
}

void displayCurrentControlMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Current Control");
    int menuSize = sizeof(curreControl) / sizeof(curreControl[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Cur(A): "); lcd.print(formatFloat(controlSettings.targetCurrent, 2));
                } else {
                    lcd.print(curreControl[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(curreControl[itemIndex].text);
                if (itemIndex == 0) lcd.print(formatFloat(controlSettings.targetCurrent, 2));
            }
        }
    }
}

void displayEditSensorMenu() {
    const MenuItem* currentMenu = nullptr;
    int menuSize = 0;
    String title = "";
    float minVal = 0.0, maxVal = 0.0;

    if (menuState.currentLevel == LEVEL_EDIT_pH) {
        currentMenu = pHsettings;
        menuSize = sizeof(pHsettings) / sizeof(pHsettings[0]);
        title = "pH Settings";
        minVal = pHMin;
        maxVal = pHMax;
    } else if (menuState.currentLevel == LEVEL_EDIT_Temp) {
        currentMenu = Tempsettings;
        menuSize = sizeof(Tempsettings) / sizeof(Tempsettings[0]);
        title = "Temperature Settings";
        minVal = tempMin;
        maxVal = tempMax;
    } else if (menuState.currentLevel == LEVEL_EDIT_Gas) {
        currentMenu = Gassettings;
        menuSize = sizeof(Gassettings) / sizeof(Gassettings[0]);
        title = "Gas Settings";
        minVal = gasMin;
        maxVal = gasMax;
    }

    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print(title);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
                if (itemIndex == 0) {
                    lcd.print("Set Min: "); lcd.print(formatFloat(minVal, 1));
                } else if (itemIndex == 1) {
                    lcd.print("Set Max: "); lcd.print(formatFloat(maxVal, 1));
                } else {
                    lcd.print(currentMenu[itemIndex].text);
                }
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
                lcd.print(currentMenu[itemIndex].text);
                if (itemIndex == 0) lcd.print(formatFloat(minVal, 1));
                else if (itemIndex == 1) lcd.print(formatFloat(maxVal, 1));
            }
        }
    }
}

void displayAddUserMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Add New User");
    int menuSize = sizeof(addUser) / sizeof(addUser[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            if (menuState.isEditing && itemIndex == menuState.currentItem) {
                lcd.print("* ");
            } else {
                lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            }
            lcd.print(addUser[itemIndex].text);
            if (itemIndex == 0) {
                lcd.print("User ");
                lcd.print(userNameIndex);
            } else if (itemIndex == 1) {
                lcd.print(profileTypes[profileTypeIndex]);
            }
        }
    }
}

void displayStartMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Start Menu");
    int menuSize = sizeof(startMenu) / sizeof(startMenu[0]);

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            lcd.print(startMenu[itemIndex].text);
        }
    }
}

void displayChooseProfileMenu() {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Choose Profile");
    int menuSize = profileCount + 1;

    if (menuState.currentItem < menuState.scrollPosition) {
        menuState.scrollPosition = menuState.currentItem;
    } else if (menuState.currentItem >= menuState.scrollPosition + 3) {
        menuState.scrollPosition = menuState.currentItem - 2;
    }

    for (int i = 0; i < 3; i++) {
        int itemIndex = menuState.scrollPosition + i;
        if (itemIndex < menuSize) {
            lcd.setCursor(0, i + 1);
            lcd.print(itemIndex == menuState.currentItem ? "-> " : "   ");
            if (itemIndex < profileCount) {
                lcd.print("User ");
                lcd.print(profiles[itemIndex].nameIndex);
                lcd.print(": ");
                lcd.print(profileTypes[profiles[itemIndex].typeIndex]);
            } else {
                lcd.print("Back");
            }
        }
    }
}

// Implementasi fungsi rotary encoder
void IRAM_ATTR readEncoderISR() {
    rotaryEncoder.readEncoder_ISR();
}

void initializeProfiles() {
    profiles[0] = {4.0, 7.0, 70, 10, 60, 5, 5.0, 200.0, 1, 0};
    for (int i = 1; i < 10; i++) {
        profiles[i] = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
    }
    tempProfile = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
}

void saveProfileToPreferences(int index) {
    if (index >= 0 && index < 10) {
        String namespaceStr = "profile_" + String(index);
        preferences.begin(namespaceStr.c_str(), false);
        preferences.putFloat("pHMin", profiles[index].pHMin);
        preferences.putFloat("pHMax", profiles[index].pHMax);
        preferences.putInt("pumpSpeed", profiles[index].pumpSpeed);
        preferences.putInt("pumpTime", profiles[index].pumpTime);
        preferences.putInt("FanSpeed", profiles[index].FanSpeed);
        preferences.putInt("FanTime", profiles[index].FanTime);
        preferences.putFloat("Volt", profiles[index].Volt);
        preferences.putFloat("Current", profiles[index].Current);
        preferences.putInt("nameIndex", profiles[index].nameIndex);
        preferences.putInt("typeIndex", profiles[index].typeIndex);
        preferences.end();
        Serial.print("Saved profile "); Serial.print(index); Serial.println(" to Preferences");
    }
}

void loadProfilesFromPreferences() {
    preferences.begin("profileCount", true);
    profileCount = preferences.getInt("count", 1);
    preferences.end();
    for (int i = 0; i < profileCount; i++) {
        String namespaceStr = "profile_" + String(i);
        preferences.begin(namespaceStr.c_str(), true);
        profiles[i].pHMin = preferences.getFloat("pHMin", 0.0);
        profiles[i].pHMax = preferences.getFloat("pHMax", 0.0);
        profiles[i].pumpSpeed = preferences.getInt("pumpSpeed", 0);
        profiles[i].pumpTime = preferences.getInt("pumpTime", 0);
        profiles[i].FanSpeed = preferences.getInt("FanSpeed", 0);
        profiles[i].FanTime = preferences.getInt("FanTime", 0);
        profiles[i].Volt = preferences.getFloat("Volt", 0.0);
        profiles[i].Current = preferences.getFloat("Current", 0.0);
        profiles[i].nameIndex = preferences.getInt("nameIndex", 0);
        profiles[i].typeIndex = preferences.getInt("typeIndex", 0);
        preferences.end();
    }
}

void saveProfileCountToPreferences() {
    preferences.begin("profileCount", false);
    preferences.putInt("count", profileCount);
    preferences.end();
}

void updateEncoderBoundaries() {
    if (menuState.isEditing) {
        rotaryEncoder.setBoundaries(-999999, 999999, false);
    } else {
        int menuSize = 0;
        switch (menuState.currentLevel) {
            case LEVEL_STATUS: menuSize = sizeof(mainMenu) / sizeof(mainMenu[0]); break;
            case LEVEL_SETUP: menuSize = sizeof(setupMenu) / sizeof(setupMenu[0]); break;
            case LEVEL_MANUAL_SETTINGS: menuSize = sizeof(manualSettingsMenu) / sizeof(manualSettingsMenu[0]); break;
            case LEVEL_SENSOR_SETTINGS: menuSize = sizeof(sensorSettingsMenu) / sizeof(sensorSettingsMenu[0]); break;
            case LEVEL_EDIT_pH: menuSize = sizeof(pHsettings) / sizeof(pHsettings[0]); break;
            case LEVEL_EDIT_Temp: menuSize = sizeof(Tempsettings) / sizeof(Tempsettings[0]); break;
            case LEVEL_EDIT_Gas: menuSize = sizeof(Gassettings) / sizeof(Gassettings[0]); break;
            case LEVEL_OUTPUT_SETTINGS: menuSize = sizeof(outputSettings) / sizeof(outputSettings[0]); break;
            case PumpSetting: menuSize = sizeof(pumpSetting) / sizeof(pumpSetting[0]); break;
            case FanSetting: menuSize = sizeof(fanSetting) / sizeof(fanSetting[0]); break;
            case VoltageControl: menuSize = sizeof(voltControl) / sizeof(voltControl[0]); break;
            case CurrentControl: menuSize = sizeof(curreControl) / sizeof(curreControl[0]); break;
            case LEVEL_CONFIG_PROFILES: menuSize = profileCount + 2; break;
            case LEVEL_chooseProfile: menuSize = profileCount + 2; break;
            case userA: menuSize = sizeof(UserA) / sizeof(UserA[0]); break;
            case NewUser: menuSize = sizeof(newUser) / sizeof(newUser[0]); break;
            case LEVEL_ADD_USER: menuSize = sizeof(addUser) / sizeof(addUser[0]); break;
            case LEVEL_START: menuSize = sizeof(startMenu) / sizeof(startMenu[0]); break;
            default: menuSize = 3; break;
        }
        rotaryEncoder.setBoundaries(0, menuSize - 1, false);
        currentMenuSize = menuSize;
    }
}

void rotary_onButtonClick() {
    static unsigned long lastTimePressed = 0;
    if (millis() - lastTimePressed < 500) return;
    lastTimePressed = millis();

    const MenuItem* currentMenu = nullptr;
    int menuSize = 0;

    switch (menuState.currentLevel) {
        case LEVEL_STATUS: currentMenu = mainMenu; menuSize = sizeof(mainMenu) / sizeof(mainMenu[0]); break;
        case LEVEL_SETUP: currentMenu = setupMenu; menuSize = sizeof(setupMenu) / sizeof(setupMenu[0]); break;
        case LEVEL_MANUAL_SETTINGS: currentMenu = manualSettingsMenu; menuSize = sizeof(manualSettingsMenu) / sizeof(manualSettingsMenu[0]); break;
        case LEVEL_SENSOR_SETTINGS: currentMenu = sensorSettingsMenu; menuSize = sizeof(sensorSettingsMenu) / sizeof(sensorSettingsMenu[0]); break;
        case LEVEL_EDIT_pH: currentMenu = pHsettings; menuSize = sizeof(pHsettings) / sizeof(pHsettings[0]); break;
        case LEVEL_EDIT_Temp: currentMenu = Tempsettings; menuSize = sizeof(Tempsettings) / sizeof(Tempsettings[0]); break;
        case LEVEL_EDIT_Gas: currentMenu = Gassettings; menuSize = sizeof(Gassettings) / sizeof(Gassettings[0]); break;
        case LEVEL_OUTPUT_SETTINGS: currentMenu = outputSettings; menuSize = sizeof(outputSettings) / sizeof(outputSettings[0]); break;
        case PumpSetting: currentMenu = pumpSetting; menuSize = sizeof(pumpSetting) / sizeof(pumpSetting[0]); break;
        case FanSetting: currentMenu = fanSetting; menuSize = sizeof(fanSetting) / sizeof(fanSetting[0]); break;
        case VoltageControl: currentMenu = voltControl; menuSize = sizeof(voltControl) / sizeof(voltControl[0]); break;
        case CurrentControl: currentMenu = curreControl; menuSize = sizeof(curreControl) / sizeof(curreControl[0]); break;
        case LEVEL_CONFIG_PROFILES: menuSize = profileCount + 2; break;
        case userA: currentMenu = UserA; menuSize = sizeof(UserA) / sizeof(UserA[0]); break;
        case NewUser: currentMenu = newUser; menuSize = sizeof(newUser) / sizeof(newUser[0]); break;
        case LEVEL_ADD_USER: currentMenu = addUser; menuSize = sizeof(addUser) / sizeof(addUser[0]); break;
        case LEVEL_START: currentMenu = startMenu; menuSize = sizeof(startMenu) / sizeof(startMenu[0]); break;
        case LEVEL_chooseProfile: currentMenu = chooseProfile; menuSize = sizeof(chooseProfile) / sizeof(chooseProfile[0]) + profileCount; break;
        default: break;
    }

    if (menuState.currentLevel == LEVEL_STATUS) {
        if (menuState.currentItem == 1) {
            if (systemStatus.isRunning) {
                stopElectrowiningProcess();
                Serial.println("Process Stopped");
                lcd.clear();
                lcd.setCursor(0, 0);
                lcd.print("Process Stopped");
                delay(2000);
                displayNeedsUpdate = true;
            } else {
                menuState.currentLevel = LEVEL_START;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                Serial.println("Navigating to Start Menu");
                displayNeedsUpdate = true;
            }
        } else if (currentMenu[menuState.currentItem].action == ACTION_NONE) {
            menuState.currentLevel = currentMenu[menuState.currentItem].nextLevel;
            menuState.currentItem = 0;
            menuState.scrollPosition = 0;
            updateEncoderBoundaries();
            displayNeedsUpdate = true;
            Serial.print("Navigating to: "); Serial.println(menuState.currentLevel);
        }
    } else if (menuState.currentLevel == LEVEL_CONFIG_PROFILES) {
        if (menuState.currentItem < profileCount) {
            currentProfileIndex = menuState.currentItem;
            menuState.currentLevel = userA;
            menuState.previousItem = menuState.currentItem;
            menuState.currentItem = 0;
            menuState.scrollPosition = 0;
            updateEncoderBoundaries();
            displayNeedsUpdate = true;
            Serial.print("Navigating to User Profile: "); Serial.println(currentProfileIndex);
        } else if (menuState.currentItem == profileCount) {
            currentProfileIndex = -1;
            tempProfile = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
            menuState.currentLevel = NewUser;
            menuState.previousItem = menuState.currentItem;
            menuState.currentItem = 0;
            menuState.scrollPosition = 0;
            updateEncoderBoundaries();
            displayNeedsUpdate = true;
            Serial.println("Navigating to Create New User");
        } else {
            menuState.currentLevel = LEVEL_SETUP;
            menuState.currentItem = menuState.previousItem;
            menuState.scrollPosition = 0;
            updateEncoderBoundaries();
            displayNeedsUpdate = true;
            Serial.println("Navigating Back to Setup");
        }
    } else if (menuState.currentLevel == LEVEL_chooseProfile) {
        if (menuState.currentItem < profileCount) {
            currentProfileIndex = menuState.currentItem;
            menuState.currentLevel = userA;
            menuState.previousItem = menuState.currentItem;
            menuState.currentItem = 0;
            menuState.scrollPosition = 0;
            updateEncoderBoundaries();
            displayNeedsUpdate = true;
            Serial.print("Navigating to User Profile: "); Serial.println(currentProfileIndex);
        } else if (menuState.currentItem == profileCount) {
            menuState.currentLevel = LEVEL_START;
            menuState.currentItem = 0;
            menuState.scrollPosition = 0;
            updateEncoderBoundaries();
            displayNeedsUpdate = true;
            Serial.println("Navigating Back to Start Menu");
        }
    } else if (currentMenu != nullptr && menuSize > 0) {
        if (currentMenu[menuState.currentItem].action == ACTION_BACK) {
            MenuLevel targetLevel = currentMenu[menuState.currentItem].nextLevel;
            if (menuState.currentLevel == LEVEL_EDIT_pH || menuState.currentLevel == LEVEL_EDIT_Temp || 
                menuState.currentLevel == LEVEL_EDIT_Gas) {
                targetLevel = LEVEL_SENSOR_SETTINGS;
            } else if (menuState.currentLevel == PumpSetting || menuState.currentLevel == FanSetting || 
                       menuState.currentLevel == VoltageControl || menuState.currentLevel == CurrentControl) {
                targetLevel = LEVEL_OUTPUT_SETTINGS;
            } else if (menuState.currentLevel == userA || menuState.currentLevel == NewUser) {
                targetLevel = LEVEL_CONFIG_PROFILES;
            } else if (menuState.currentLevel == LEVEL_SENSOR_SETTINGS || menuState.currentLevel == LEVEL_OUTPUT_SETTINGS) {
                targetLevel = LEVEL_MANUAL_SETTINGS;
            } else if (menuState.currentLevel == LEVEL_SETUP) {
                targetLevel = LEVEL_STATUS;
            } else if (menuState.currentLevel == LEVEL_ADD_USER) {
                targetLevel = currentProfileIndex == -1 ? NewUser : userA;
                menuState.currentItem = 0;
            }
            menuState.currentLevel = targetLevel;
            menuState.previousItem = menuState.currentItem;
            menuState.isEditing = false;
            menuState.scrollPosition = 0;
            updateEncoderBoundaries();
            displayNeedsUpdate = true;
            Serial.print("Navigating Back to: "); Serial.println(targetLevel);
        } else if (currentMenu[menuState.currentItem].action == ACTION_SAVE) {
            if (menuState.currentLevel == LEVEL_ADD_USER) {
                if (profileCount < 10) {
                    profiles[profileCount] = tempProfile;
                    profiles[profileCount].nameIndex = userNameIndex;
                    profiles[profileCount].typeIndex = profileTypeIndex;
                    saveProfileToPreferences(profileCount);
                    profileCount++;
                    saveProfileCountToPreferences();
                    menuState.currentLevel = LEVEL_CONFIG_PROFILES;
                    menuState.currentItem = 0;
                    menuState.scrollPosition = 0;
                    updateEncoderBoundaries();
                    displayNeedsUpdate = true;
                    Serial.println("New Profile Saved");
                } else {
                    Serial.println("Profile limit reached");
                    lcd.clear();
                    lcd.setCursor(0, 0);
                    lcd.print("Profile Limit");
                    lcd.setCursor(0, 1);
                    lcd.print("Reached!");
                    delay(2000);
                    displayNeedsUpdate = true;
                }
            } else if (menuState.currentLevel == userA) {
                if (currentProfileIndex >= 0) {
                    saveProfileToPreferences(currentProfileIndex);
                    menuState.currentLevel = LEVEL_CONFIG_PROFILES;
                    menuState.currentItem = 0;
                    menuState.scrollPosition = 0;
                    updateEncoderBoundaries();
                    displayNeedsUpdate = true;
                    Serial.println("Profile Updated and Saved to Preferences");
                }
            } else {
                menuState.currentLevel = LEVEL_STATUS;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
                Serial.println("Save & Exit");
            }
        } else if (currentMenu[menuState.currentItem].action == ACTION_RESET && menuState.currentLevel == userA) {
            if (currentProfileIndex >= 0) {
                for (int i = currentProfileIndex; i < profileCount - 1; i++) {
                    profiles[i] = profiles[i + 1];
                    saveProfileToPreferences(i);
                }
                profiles[profileCount - 1] = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
                profileCount--;
                saveProfileCountToPreferences();
                menuState.currentLevel = LEVEL_CONFIG_PROFILES;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
                Serial.println("Profile Deleted");
            }
        } else if (currentMenu[menuState.currentItem].action == ACTION_RESET && menuState.currentLevel == NewUser) {
            tempProfile = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
            displayNeedsUpdate = true;
            Serial.println("Profile Reset");
        } else if (currentMenu[menuState.currentItem].action == ACTION_START && menuState.currentLevel == userA) {
            if (currentProfileIndex >= 0) {
                // Load profile settings to controlling system
                loadProfileSettings(profiles[currentProfileIndex]);
                startElectrowiningProcess();

                menuState.currentLevel = LEVEL_STATUS;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
                Serial.println("Profile Started");
                lcd.clear();
                lcd.setCursor(0, 0);
                lcd.print("Profile Started");
                delay(2000);
            }
        } else if (currentMenu[menuState.currentItem].action == ACTION_START && menuState.currentLevel == LEVEL_MANUAL_SETTINGS) {
            // Apply manual settings and start process
            applyManualSettings();
            startElectrowiningProcess();

            menuState.currentLevel = LEVEL_STATUS;
            menuState.currentItem = 0;
            menuState.scrollPosition = 0;
            updateEncoderBoundaries();
            displayNeedsUpdate = true;
            Serial.println("Manual Process Started");
            lcd.clear();
            lcd.setCursor(0, 0);
            lcd.print("Process Started");
            delay(2000);
        } else if (currentMenu[menuState.currentItem].nextLevel == LEVEL_ADD_USER && 
                   (menuState.currentLevel == userA || menuState.currentLevel == NewUser)) {
            if (menuState.currentLevel == userA && currentProfileIndex >= 0) {
                profiles[currentProfileIndex] = tempProfile;
                saveProfileToPreferences(currentProfileIndex);
                menuState.currentLevel = LEVEL_CONFIG_PROFILES;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
                Serial.println("Existing Profile Saved to Preferences");
            } else {
                menuState.currentLevel = LEVEL_ADD_USER;
                menuState.previousItem = menuState.currentItem;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
                Serial.println("Navigating to Add New User");
            }
        } else if (currentMenu[menuState.currentItem].action == ACTION_NONE) {
            if (currentMenu[menuState.currentItem].isEditable) {
                menuState.isEditing = !menuState.isEditing;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
                Serial.println(menuState.isEditing ? "Entering Edit Mode" : "Exiting Edit Mode");
            } else {
                menuState.currentLevel = currentMenu[menuState.currentItem].nextLevel;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
                Serial.print("Navigating to: "); Serial.println(menuState.currentLevel);
            }
        } else if (currentMenu[menuState.currentItem].action == ACTION_DELETE && menuState.currentLevel == userA) {
            if (currentProfileIndex >= 0 && currentProfileIndex < profileCount) {
                for (int i = currentProfileIndex; i < profileCount - 1; i++) {
                    profiles[i] = profiles[i + 1];
                }
                profileCount--;
                saveProfileCountToPreferences();
                for (int i = 0; i < profileCount; i++) {
                    saveProfileToPreferences(i);
                }
                Serial.println("Profile Deleted");
                menuState.currentLevel = LEVEL_CONFIG_PROFILES;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
            }
        } else if (currentMenu[menuState.currentItem].action == ACTION_UPDATE && menuState.currentLevel == userA) {
            if (currentProfileIndex >= 0) {
                saveProfileToPreferences(currentProfileIndex);
                menuState.currentLevel = LEVEL_CONFIG_PROFILES;
                menuState.currentItem = 0;
                menuState.scrollPosition = 0;
                updateEncoderBoundaries();
                displayNeedsUpdate = true;
                Serial.println("Profile Updated and Saved to Preferences");
            }
        } else if (currentMenu[menuState.currentItem].action == ACTION_RESET && menuState.currentLevel == LEVEL_SETUP) {
            profileCount = 1;
            saveProfileCountToPreferences();
            for (int i = 1; i < 10; i++) {
                String namespaceStr = "profile_" + String(i);
                preferences.begin(namespaceStr.c_str(), false);
                preferences.clear();
                preferences.end();
            }
            profiles[0] = {4.0, 7.0, 70, 10, 60, 5, 5.0, 200.0, 1, 0};
            saveProfileToPreferences(0);
            for (int i = 1; i < 10; i++) {
                profiles[i] = {0.0, 0.0, 0, 0, 0, 0, 0.0, 0.0, 0, 0};
            }
            lcd.clear();
            lcd.setCursor(0, 0);
            lcd.print("Profiles Reset");
            delay(2000);
            menuState.currentItem = 0;
            menuState.scrollPosition = 0;
            displayNeedsUpdate = true;
            Serial.println("Profiles Reset to Default");
            Serial.print("profileCount = ");
            Serial.println(profileCount);
        }
    }
}

void rotary_loop() {
    if (rotaryEncoder.encoderChanged()) {
        int currentPosition = rotaryEncoder.readEncoder();
        int delta = currentPosition - previousEncoderPosition;
        previousEncoderPosition = currentPosition;

        UserProfile* targetProfile = (menuState.currentLevel == userA && currentProfileIndex >= 0) ? &profiles[currentProfileIndex] : &tempProfile;

        if (menuState.isEditing) {
            float increment = (delta > 0) ? 0.1 : -0.1;
            int intIncrement = (delta > 0) ? 1 : -1;
            if (menuState.currentLevel == LEVEL_EDIT_pH) {
                if (menuState.currentItem == 0) {
                    pHMin += increment;
                    pHMin = constrain(pHMin, 0.0, 14.0);
                    Serial.print("pH Min: "); Serial.println(pHMin);
                } else if (menuState.currentItem == 1) {
                    pHMax += increment;
                    pHMax = constrain(pHMax, 0.0, 14.0);
                    Serial.print("pH Max: "); Serial.println(pHMax);
                }
            } else if (menuState.currentLevel == LEVEL_EDIT_Temp) {
                if (menuState.currentItem == 0) {
                    tempMin += increment;
                    tempMin = constrain(tempMin, -10.0, 100.0);
                    Serial.print("Temp Min: "); Serial.println(tempMin);
                } else if (menuState.currentItem == 1) {
                    tempMax += increment;
                    tempMax = constrain(tempMax, -10.0, 100.0);
                    Serial.print("Temp Max: "); Serial.println(tempMax);
                }
            } else if (menuState.currentLevel == LEVEL_EDIT_Gas) {
                if (menuState.currentItem == 0) {
                    gasMin += increment;
                    gasMin = constrain(gasMin, 0.0, 1000.0);
                    Serial.print("Gas Min: "); Serial.println(gasMin);
                } else if (menuState.currentItem == 1) {
                    gasMax += increment;
                    gasMax = constrain(gasMax, 0.0, 1000.0);
                    Serial.print("Gas Max: "); Serial.println(gasMax);
                }
            } else if (menuState.currentLevel == PumpSetting) {
                if (menuState.currentItem == 0) {
                    controlSettings.pumpSpeed += intIncrement;
                    controlSettings.pumpSpeed = constrain(controlSettings.pumpSpeed, 0, 100);
                    Serial.print("Pump Speed: "); Serial.println(controlSettings.pumpSpeed);
                } else if (menuState.currentItem == 1) {
                    controlSettings.pumpTime += intIncrement;
                    controlSettings.pumpTime = constrain(controlSettings.pumpTime, 0, 60);
                    Serial.print("Pump Time: "); Serial.println(controlSettings.pumpTime);
                }
            } else if (menuState.currentLevel == FanSetting) {
                if (menuState.currentItem == 0) {
                    controlSettings.fanSpeed += intIncrement;
                    controlSettings.fanSpeed = constrain(controlSettings.fanSpeed, 0, 100);
                    Serial.print("Fan Speed: "); Serial.println(controlSettings.fanSpeed);
                } else if (menuState.currentItem == 1) {
                    controlSettings.fanTime += intIncrement;
                    controlSettings.fanTime = constrain(controlSettings.fanTime, 0, 60);
                    Serial.print("Fan Time: "); Serial.println(controlSettings.fanTime);
                }
            } else if (menuState.currentLevel == VoltageControl) {
                if (menuState.currentItem == 0) {
                    controlSettings.targetVoltage += increment;
                    controlSettings.targetVoltage = constrain(controlSettings.targetVoltage, 0.0, 60.0);
                    Serial.print("Voltage: "); Serial.println(controlSettings.targetVoltage);
                }
            } else if (menuState.currentLevel == CurrentControl) {
                if (menuState.currentItem == 0) {
                    controlSettings.targetCurrent += increment;
                    controlSettings.targetCurrent = constrain(controlSettings.targetCurrent, 0.0, 20.0);
                    Serial.print("Current: "); Serial.println(controlSettings.targetCurrent);
                }
            } else if (menuState.currentLevel == userA || menuState.currentLevel == NewUser) {
                if (menuState.currentItem == 0) {
                    targetProfile->pHMin += increment;
                    targetProfile->pHMin = constrain(targetProfile->pHMin, 0.0, 14.0);
                    Serial.print("pH Min: "); Serial.println(targetProfile->pHMin);
                } else if (menuState.currentItem == 1) {
                    targetProfile->pHMax += increment;
                    targetProfile->pHMax = constrain(targetProfile->pHMax, 0.0, 14.0);
                    Serial.print("pH Max: "); Serial.println(targetProfile->pHMax);
                } else if (menuState.currentItem == 2) {
                    targetProfile->pumpSpeed += intIncrement;
                    targetProfile->pumpSpeed = constrain(targetProfile->pumpSpeed, 0, 100);
                    Serial.print("Pump Speed: "); Serial.println(targetProfile->pumpSpeed);
                } else if (menuState.currentItem == 3) {
                    targetProfile->pumpTime += intIncrement;
                    targetProfile->pumpTime = constrain(targetProfile->pumpTime, 0, 60);
                    Serial.print("Pump Time: "); Serial.println(targetProfile->pumpTime);
                } else if (menuState.currentItem == 4) {
                    targetProfile->FanSpeed += intIncrement;
                    targetProfile->FanSpeed = constrain(targetProfile->FanSpeed, 0, 100);
                    Serial.print("Fan Speed: "); Serial.println(targetProfile->FanSpeed);
                } else if (menuState.currentItem == 5) {
                    targetProfile->FanTime += intIncrement;
                    targetProfile->FanTime = constrain(targetProfile->FanTime, 0, 60);
                    Serial.print("Fan Time: "); Serial.println(targetProfile->FanTime);
                } else if (menuState.currentItem == 6) {
                    targetProfile->Volt += increment;
                    targetProfile->Volt = constrain(targetProfile->Volt, 0.0, 12.0);
                    Serial.print("Voltage: "); Serial.println(targetProfile->Volt);
                } else if (menuState.currentItem == 7) {
                    targetProfile->Current += increment;
                    targetProfile->Current = constrain(targetProfile->Current, 0.0, 1000.0);
                    Serial.print("Current: "); Serial.println(targetProfile->Current);
                }
            } else if (menuState.currentLevel == LEVEL_ADD_USER) {
                if (menuState.currentItem == 0) {
                    userNameIndex += intIncrement;
                    userNameIndex = constrain(userNameIndex, 1, 10);
                    Serial.print("User Name: User "); Serial.println(userNameIndex);
                } else if (menuState.currentItem == 1) {
                    profileTypeIndex += intIncrement;
                    profileTypeIndex = constrain(profileTypeIndex, 0, 3);
                    Serial.print("Profile Type: "); Serial.println(profileTypes[profileTypeIndex]);
                }
            }
            displayNeedsUpdate = true;
        } else {
            menuState.currentItem = currentPosition;
            menuState.currentItem = constrain(menuState.currentItem, 0, currentMenuSize - 1);
            displayNeedsUpdate = true;
        }
        Serial.print("Current Level: "); Serial.println(menuState.currentLevel);
        Serial.print("Current Item: "); Serial.println(menuState.currentItem);
    }

    if (rotaryEncoder.isEncoderButtonClicked()) {
        rotary_onButtonClick();
    }
}