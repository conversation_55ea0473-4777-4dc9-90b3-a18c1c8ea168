<!DOCTYPE html>
<html>
<head>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script type="text/javascript" src="charts.js"></script>
    <script type="text/javascript" src="logic.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css">
    <title>XY6020 Monitoring</title>
    <style>
        .chart-container {
            position: relative;
            height: 250px;
            width: 100%;
            margin-bottom: 20px;
        }
        .chart-controls {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .chart-controls select, .chart-controls input {
            background-color: #363f4f;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            margin-left: 10px;
        }
        .chart-controls label {
            color: #ccc;
            margin-right: 5px;
        }
    </style>
</head>
<body onload="initCharts()">
    <center>
        <h1 style="font-size: 50px; color: gray">XY6020 Monitoring <span id="connection-state"
                style="display: inline; color: lightgreen;">&#10003;</span>
        </h1>
        
        <div class="my-container" style="width: 80%;">
            <span>Real-time Monitoring</span>
            
            <div class="chart-controls">
                <div>
                    <label for="update-interval">Update Interval:</label>
                    <select id="update-interval" onchange="updateChartSettings()">
                        <option value="500">0.5 seconds</option>
                        <option value="1000" selected>1 second</option>
                        <option value="2000">2 seconds</option>
                        <option value="5000">5 seconds</option>
                    </select>
                </div>
                <div>
                    <label for="time-range">Time Range:</label>
                    <select id="time-range" onchange="updateChartSettings()">
                        <option value="30">30 seconds</option>
                        <option value="60" selected>1 minute</option>
                        <option value="300">5 minutes</option>
                        <option value="600">10 minutes</option>
                    </select>
                </div>
                <div>
                    <button class="small-button" onclick="clearChartData()">Clear Data</button>
                </div>
            </div>
            
            <div class="chart-container">
                <canvas id="voltageChart"></canvas>
            </div>
            
            <div class="chart-container">
                <canvas id="currentChart"></canvas>
            </div>
            
            <div class="chart-container">
                <canvas id="powerChart"></canvas>
            </div>
        </div>
        
        <br>
        <div style="width: 60%; margin:0px; padding: 0px;">
            <button class="my-button small" id="back-button" onclick="goBack()">Back</button>
            <button class="my-button small" id="refresh-button" onclick="togglePause()">Pause</button>
        </div>
    </center>
</body>
</html>
