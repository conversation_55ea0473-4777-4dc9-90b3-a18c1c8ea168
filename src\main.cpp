#include <Arduino.h>
#include <LiquidCrystal_I2C.h>
#include <Preferences.h>
#include "AiEsp32RotaryEncoder.h"
#include "menu.h"
#include "display.h"
#include "controlling.h"

// Definisi pin rotary encoder (tetap di main.cpp untuk kompatibilitas)
#define ROTARY_ENCODER_A_PIN 32
#define ROTARY_ENCODER_B_PIN 33
#define ROTARY_ENCODER_BUTTON_PIN 25
#define ROTARY_ENCODER_VCC_PIN -1
#define ROTARY_ENCODER_STEPS 4

// Inisialisasi objek global
AiEsp32RotaryEncoder rotaryEncoder(ROTARY_ENCODER_A_PIN, ROTARY_ENCODER_B_PIN, ROTARY_ENCODER_BUTTON_PIN, ROTARY_ENCODER_VCC_PIN, ROTARY_ENCODER_STEPS);
LiquidCrystal_I2C lcd(0x27, 20, 4);
Preferences preferences;

// Variabel global
float pH = 0.0;
float temp = 0.0;
int pumpSpeed = 0;
int fanSpeed = 0;
int voltage = 0;
float current = 0.0;
float pHMin = 0.0, pHMax = 0.0;
float tempMin = 0.0, tempMax = 0.0;
float gasMin = 0.0, gasMax = 0.0;
int pumpSpeedSetting = 0;
int fanTimeSetting = 0;
float voltageSetting = 0.0;
float currentSetting = 0.0;
int currentMenuSize = 0;
int previousEncoderPosition = 0;
bool displayNeedsUpdate = true;
int userNameIndex = 1;
int profileTypeIndex = 0;
UserProfile profiles[10];
int profileCount = 1;
UserProfile tempProfile;
int currentProfileIndex = -1;
bool isRunning = false;
MenuState menuState = {LEVEL_STATUS, 0, 0, 0, false, 0};
const char* profileTypes[] = {"Gold", "Silver", "Perak", "Perunggu"};

void setup() {
    Serial.begin(115200);
    lcd.init();
    lcd.backlight();
    rotaryEncoder.begin();
    rotaryEncoder.setup(readEncoderISR);
    rotaryEncoder.setAcceleration(250);

    // Initialize controlling system
    initializeControlling();

    // Test XY6020 communication after initialization
    delay(2000);  // Give time for initialization
    testXY6020Communication();

    loadProfilesFromPreferences();
    updateEncoderBoundaries();
    displayMainScreen();
    displayNeedsUpdate = false;
}

void loop() {
    // Run controlling system tasks (includes XY6020 communication)
    controllingTask();

    // Periodic XY6020 status logging (every 10 seconds)
    static unsigned long lastStatusLog = 0;
    if (millis() - lastStatusLog >= 10000) {
        lastStatusLog = millis();
        Serial.printf("XY6020 Status: %s | V:%.2f | I:%.2f | P:%.1f\n",
                     isXY6020Connected() ? "CONN" : "DISC",
                     getXY6020Voltage(), getXY6020Current(), getXY6020Power());
    }

    // Sync old global variables with new controlling system data for display compatibility
    pH = sensorData.pH;
    temp = sensorData.temperature;
    voltage = (int)sensorData.voltage;  // Convert to int for backward compatibility
    current = sensorData.current;
    pumpSpeed = controlSettings.pumpSpeed;
    fanSpeed = controlSettings.fanSpeed;
    isRunning = systemStatus.isRunning;

    // Handle rotary encoder and menu
    rotary_loop();

    if (displayNeedsUpdate) {
        switch (menuState.currentLevel) {
            case LEVEL_STATUS: displayMainScreen(); Serial.println("Current Menu: LEVEL_STATUS"); break;
            case LEVEL_SETUP: displaySetupMenu(); Serial.println("Current Menu: LEVEL_SETUP"); break;
            case LEVEL_MANUAL_SETTINGS: displayManualSettingsMenu(); Serial.println("Current Menu: LEVEL_MANUAL_SETTINGS"); break;
            case LEVEL_SENSOR_SETTINGS: displaySensorSettingsMenu(); Serial.println("Current Menu: LEVEL_SENSOR_SETTINGS"); break;
            case LEVEL_OUTPUT_SETTINGS: displayOutputSettingsMenu(); Serial.println("Current Menu: LEVEL_OUTPUT_SETTINGS"); break;
            case LEVEL_CONFIG_PROFILES: displayConfigProfilesMenu(); Serial.println("Current Menu: LEVEL_CONFIG_PROFILES"); break;
            case userA: displayUserAMenu(); Serial.println("Current Menu: userA"); break;
            case NewUser: displayNewUserMenu(); Serial.println("Current Menu: NewUser"); break;
            case PumpSetting: displayPumpSettingMenu(); Serial.println("Current Menu: PumpSetting"); break;
            case FanSetting: displayFanSettingMenu(); Serial.println("Current Menu: FanSetting"); break;
            case VoltageControl: displayVoltageControlMenu(); Serial.println("Current Menu: VoltageControl"); break;
            case CurrentControl: displayCurrentControlMenu(); Serial.println("Current Menu: CurrentControl"); break;
            case LEVEL_EDIT_pH:
            case LEVEL_EDIT_Temp:
            case LEVEL_EDIT_Gas: displayEditSensorMenu(); Serial.println("Current Menu: EDIT Sensor"); break;
            case LEVEL_ADD_USER: displayAddUserMenu(); Serial.println("Current Menu: LEVEL_ADD_USER"); break;
            case LEVEL_START: displayStartMenu(); Serial.println("Current Menu: LEVEL_START"); break;
            case LEVEL_chooseProfile: displayChooseProfileMenu(); Serial.println("Current Menu: LEVEL_chooseProfile"); break;
            default: break;
        }
        displayNeedsUpdate = false;
    }
    delay(50); // Reduced delay for better responsiveness
}