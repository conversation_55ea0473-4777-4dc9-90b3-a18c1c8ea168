; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32doit-devkit-v1]
platform = espressif32
board = esp32doit-devkit-v1
framework = arduino
lib_deps =
    igorantolic/Ai Esp32 Rotary Encoder@^1.7
    marcoschwartz/LiquidCrystal_I2C @ ^1.1.4
    emelianov/modbus-esp8266@^4.1.0
