; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
lib_deps =
	emelianov/modbus-esp8266@^4.1.0
	knolleary/PubSubClient@^2.8
	bblanchon/Ard<PERSON><PERSON><PERSON><PERSON>@^7.0.4
	khoih-prog/ESP_MultiResetDetector@^1.3.2
monitor_speed = 115200
build_flags =
	-D ESP32
