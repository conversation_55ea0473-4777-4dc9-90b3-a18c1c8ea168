# Implementasi Controlling System dan XY6020 Integration

## Overview
Proyek ini telah berhasil direfactor untuk memisahkan logika controlling ke dalam file terpisah dan mengintegrasikan modul XY6020 untuk kontrol arus dan tegangan dalam sistem elektrowining.

## Struktur File Baru

### 1. File Controlling System
- **`include/controlling.h`** - Header file untuk sistem controlling
- **`src/controlling.cpp`** - Implementasi sistem controlling
- **`include/xy6020.h`** - Header file untuk modul XY6020
- **`src/xy6020.cpp`** - Implementasi driver XY6020

### 2. File yang Dimodifikasi
- **`src/main.cpp`** - Ditambahkan inisialisasi dan task controlling
- **`src/display.cpp`** - Diupdate untuk menggunakan data dari sistem controlling
- **`include/display.h`** - Ditambahkan deklarasi variabel controlling
- **`platformio.ini`** - Ditambahkan dependency ModbusRTU

## Fitur Utama

### 1. Sistem Controlling (`controlling.cpp`)
- **Sensor Management**: Pembacaan pH dan temperature
- **Actuator Control**: Kontrol pump dan fan dengan PWM
- **XY6020 Integration**: Kontrol voltage dan current melalui Modbus
- **Process Control**: Logika start/stop proses elektrowining
- **Safety Monitoring**: Pemantauan batas-batas keamanan

### 2. XY6020 Driver (`xy6020.cpp`)
- **Modbus Communication**: Komunikasi dengan modul XY6020 via Serial2
- **Voltage Control**: Set target voltage (0-60V)
- **Current Control**: Set max current (0-20A)
- **Output Control**: Enable/disable output
- **Status Monitoring**: Monitoring koneksi dan pembacaan nilai aktual

### 3. Data Structures
```cpp
// Status sistem
struct SystemStatus {
    bool isRunning;
    bool pumpActive;
    bool fanActive;
    bool xy6020Connected;
    unsigned long processStartTime;
    unsigned long lastSensorRead;
    unsigned long lastControlUpdate;
};

// Data sensor
struct SensorData {
    float pH;
    float temperature;
    float voltage;
    float current;
    float power;
    unsigned long lastUpdate;
};

// Pengaturan kontrol
struct ControlSettings {
    float targetVoltage;
    float targetCurrent;
    int pumpSpeed;      // 0-100%
    int fanSpeed;       // 0-100%
    int pumpTime;       // minutes
    int fanTime;        // minutes
    float pHMin, pHMax;
    float tempMin, tempMax;
};
```

## Complete Pin Configuration

### 📡 Communication Interfaces

#### XY6020 Modbus (Power Supply Control)
- **RX Pin**: GPIO16
- **TX Pin**: GPIO17
- **Baud Rate**: 115200
- **Slave Address**: 1

#### LCD Display (I2C)
- **SDA Pin**: GPIO21
- **SCL Pin**: GPIO22
- **I2C Address**: 0x27 (typical for PCF8574)

#### Rotary Encoder (User Interface)
- **Encoder A**: GPIO2
- **Encoder B**: GPIO4
- **Button**: GPIO15

### 🔧 Motor Drivers (L298N)

#### Water Pump Motor Driver
- **Enable Pin (PWM)**: GPIO5
- **Direction IN1**: GPIO18
- **Direction IN2**: GPIO19

#### Fan Motor Driver
- **Enable Pin (PWM)**: GPIO14
- **Direction IN1**: GPIO27
- **Direction IN2**: GPIO26

### 📊 Sensors (Analog Inputs)
- **pH Sensor**: GPIO13 (ADC1_CH5)
- **Temperature Sensor**: GPIO12 (ADC2_CH5)
- **MQ Gas Sensor**: GPIO39 (ADC1_CH3)

### 📋 Complete Pin Mapping Table

| GPIO | Function | Type | Description |
|------|----------|------|-------------|
| GPIO2 | Rotary Encoder A | Digital Input | Encoder channel A |
| GPIO4 | Rotary Encoder B | Digital Input | Encoder channel B |
| GPIO5 | Pump Motor EN | PWM Output | Water pump speed control |
| GPIO12 | Temperature Sensor | Analog Input | Temperature sensor reading |
| GPIO13 | pH Sensor | Analog Input | pH sensor reading |
| GPIO14 | Fan Motor EN | PWM Output | Fan motor speed control |
| GPIO15 | Encoder Button | Digital Input | Rotary encoder button |
| GPIO16 | XY6020 RX | UART RX | Modbus communication |
| GPIO17 | XY6020 TX | UART TX | Modbus communication |
| GPIO18 | Pump Motor IN1 | Digital Output | Water pump direction 1 |
| GPIO19 | Pump Motor IN2 | Digital Output | Water pump direction 2 |
| GPIO21 | LCD SDA | I2C SDA | LCD display data |
| GPIO22 | LCD SCL | I2C SCL | LCD display clock |
| GPIO26 | Fan Motor IN2 | Digital Output | Fan motor direction 2 |
| GPIO27 | Fan Motor IN1 | Digital Output | Fan motor direction 1 |
| GPIO39 | MQ Gas Sensor | Analog Input | Gas sensor reading |

## Fungsi Utama

### Controlling Functions
- `initializeControlling()` - Inisialisasi sistem controlling
- `controllingTask()` - Task utama yang dipanggil di loop()
- `startElectrowiningProcess()` - Memulai proses elektrowining
- `stopElectrowiningProcess()` - Menghentikan proses
- `loadProfileSettings()` - Load pengaturan dari profile
- `checkSafetyLimits()` - Pemeriksaan batas keamanan

### XY6020 Functions
- `setXY6020Voltage(float voltage)` - Set target voltage
- `setXY6020Current(float current)` - Set max current
- `setXY6020Output(bool enabled)` - Enable/disable output
- `getXY6020Voltage()` - Baca voltage aktual
- `getXY6020Current()` - Baca current aktual
- `isXY6020Connected()` - Status koneksi

### Sensor Functions
- `readSensors()` - Baca semua sensor
- `readPH()` - Baca sensor pH
- `readTemperature()` - Baca sensor temperature

### Actuator Functions
- `setPumpSpeed(int speed)` - Set kecepatan pump (0-100%)
- `setFanSpeed(int speed)` - Set kecepatan fan (0-100%)
- `setPumpDirection(bool forward)` - Set arah putaran pump
- `setFanDirection(bool forward)` - Set arah putaran fan
- `emergencyStopMotors()` - Emergency stop semua motor
- `stopAllActuators()` - Stop semua actuator

## Integrasi dengan Menu System

Display system telah diupdate untuk menggunakan data dari controlling system:
- Main screen menampilkan data real-time dari `sensorData`
- Menu settings mengupdate `controlSettings`
- Start/stop process menggunakan fungsi controlling

## Testing dan Debugging

### Compile Status
✅ **BERHASIL** - Kode berhasil dikompilasi tanpa error

### Debugging Features
- Serial output untuk monitoring status
- `logSystemStatus()` untuk debugging
- Error handling untuk komunikasi Modbus

## Motor Driver Configuration - L298N Dual H-Bridge

### L298N Module Overview
L298N adalah dual H-bridge motor driver yang dapat mengontrol 2 motor DC secara independen. Setiap motor memiliki:
- **1 pin Enable (ENA/ENB)** - untuk kontrol kecepatan via PWM
- **2 pin Input (IN1,IN2 / IN3,IN4)** - untuk kontrol arah putaran

### Pin Mapping ESP32 ke L298N

#### Motor A (Pump Motor)
```
ESP32 Pin      L298N Pin       Fungsi
GPIO26    ->   ENA            Enable Motor A (PWM Speed Control)
GPIO27    ->   IN1            Input 1 Motor A (Direction Control)
GPIO14    ->   IN2            Input 2 Motor A (Direction Control)
GPIO13    ->   Relay          Pump Power Relay (Optional Safety)
```

#### Motor B (Fan Motor)
```
ESP32 Pin      L298N Pin       Fungsi
GPIO25    ->   ENB            Enable Motor B (PWM Speed Control)
GPIO33    ->   IN3            Input 3 Motor B (Direction Control)
GPIO32    ->   IN4            Input 4 Motor B (Direction Control)
GPIO12    ->   Relay          Fan Power Relay (Optional Safety)
```

#### Power Supply
```
ESP32 5V  ->   L298N VCC      Logic Power (5V)
12V PSU   ->   L298N 12V      Motor Power (12V untuk motor besar)
GND       ->   L298N GND      Common Ground
```

### Wiring Diagram L298N
```
                    L298N Module
    ┌─────────────────────────────────────┐
    │  12V  GND  5V   ENA IN1 IN2 ENB IN3 IN4 │
    │   │    │   │     │   │   │   │   │   │  │
    │   │    │   │     │   │   │   │   │   │  │
    └───┼────┼───┼─────┼───┼───┼───┼───┼───┼──┘
        │    │   │     │   │   │   │   │   │
        │    │   │     │   │   │   │   │   │
    12V │    │   │     │   │   │   │   │   │
    PSU │    │   │     │   │   │   │   │   │
        │    │   │     │   │   │   │   │   │
        │    │   └─────┼───┼───┼───┼───┼───┼── ESP32 5V
        │    │         │   │   │   │   │   │
        │    └─────────┼───┼───┼───┼───┼───┼── ESP32 GND
        │              │   │   │   │   │   │
        └──────────────┼───┼───┼───┼───┼───┼── 12V Power
                       │   │   │   │   │   │
                    GPIO26 │   │   │   │   │
                        GPIO27 │   │   │   │
                            GPIO14 │   │   │
                                GPIO25 │   │
                                    GPIO33 │
                                        GPIO32

    Motor A (Pump)     Motor B (Fan)
    ┌─────────────┐   ┌─────────────┐
    │      +      │   │      +      │
    │             │   │             │
    │      -      │   │      -      │
    └─────────────┘   └─────────────┘
         │   │             │   │
         │   │             │   │
    ┌────┴───┴────┐   ┌────┴───┴────┐
    │  OUT1  OUT2 │   │  OUT3  OUT4 │
    └─────────────┘   └─────────────┘
```

### Kontrol Motor dengan L298N

#### 1. Speed Control (Kecepatan)
- Menggunakan **PWM pada pin Enable (ENA/ENB)**
- Range: 0-255 (0% - 100%)
- Frekuensi PWM: 1000Hz

#### 2. Direction Control (Arah Putaran)

**Motor A (Pump):**
```cpp
// Forward (Maju)
digitalWrite(PUMP_MOTOR_IN1_PIN, HIGH);  // IN1 = HIGH
digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);   // IN2 = LOW

// Reverse (Mundur)
digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);   // IN1 = LOW
digitalWrite(PUMP_MOTOR_IN2_PIN, HIGH);  // IN2 = HIGH

// Stop (Berhenti)
digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);   // IN1 = LOW
digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);   // IN2 = LOW

// Brake (Rem - kedua pin HIGH)
digitalWrite(PUMP_MOTOR_IN1_PIN, HIGH);  // IN1 = HIGH
digitalWrite(PUMP_MOTOR_IN2_PIN, HIGH);  // IN2 = HIGH
```

**Motor B (Fan):**
```cpp
// Forward (Maju)
digitalWrite(FAN_MOTOR_IN1_PIN, HIGH);   // IN3 = HIGH
digitalWrite(FAN_MOTOR_IN2_PIN, LOW);    // IN4 = LOW

// Reverse (Mundur)
digitalWrite(FAN_MOTOR_IN1_PIN, LOW);    // IN3 = LOW
digitalWrite(FAN_MOTOR_IN2_PIN, HIGH);   // IN4 = HIGH

// Stop (Berhenti)
digitalWrite(FAN_MOTOR_IN1_PIN, LOW);    // IN3 = LOW
digitalWrite(FAN_MOTOR_IN2_PIN, LOW);    // IN4 = LOW
```

### Truth Table L298N
| ENA/ENB | IN1/IN3 | IN2/IN4 | Motor State |
|---------|---------|---------|-------------|
| LOW     | X       | X       | Stop        |
| HIGH    | LOW     | LOW     | Stop        |
| HIGH    | LOW     | HIGH    | Reverse     |
| HIGH    | HIGH    | LOW     | Forward     |
| HIGH    | HIGH    | HIGH    | Brake       |

### Safety Features
1. **Relay Control**: GPIO13 (Pump), GPIO12 (Fan)
   - Memutus power supply ke motor saat emergency
   - Proteksi tambahan selain kontrol L298N

2. **Emergency Stop**:
   - Set semua IN pins ke LOW
   - Set Enable pins ke LOW (PWM = 0)
   - Matikan relay

3. **Overcurrent Protection**:
   - L298N memiliki proteksi thermal built-in
   - Monitor arus motor jika diperlukan

### Contoh Implementasi Kode

#### Inisialisasi Motor
```cpp
void initializeMotors() {
    // Setup PWM untuk speed control
    ledcSetup(PWM_CHANNEL_PUMP, 1000, 8);  // Channel 0, 1kHz, 8-bit
    ledcSetup(PWM_CHANNEL_FAN, 1000, 8);   // Channel 1, 1kHz, 8-bit

    // Attach PWM ke Enable pins
    ledcAttachPin(PUMP_MOTOR_EN_PIN, PWM_CHANNEL_PUMP);  // GPIO26
    ledcAttachPin(FAN_MOTOR_EN_PIN, PWM_CHANNEL_FAN);    // GPIO25

    // Setup direction pins sebagai output
    pinMode(PUMP_MOTOR_IN1_PIN, OUTPUT);  // GPIO27
    pinMode(PUMP_MOTOR_IN2_PIN, OUTPUT);  // GPIO14
    pinMode(FAN_MOTOR_IN1_PIN, OUTPUT);   // GPIO33
    pinMode(FAN_MOTOR_IN2_PIN, OUTPUT);   // GPIO32

    // Setup relay pins (optional)
    pinMode(PUMP_RELAY_PIN, OUTPUT);      // GPIO13
    pinMode(FAN_RELAY_PIN, OUTPUT);       // GPIO12

    // Initialize semua motor dalam keadaan stop
    stopAllMotors();
}
```

#### Kontrol Pump Motor
```cpp
void setPumpMotor(int speed, bool forward) {
    // Constrain speed 0-100%
    speed = constrain(speed, 0, 100);
    int pwmValue = map(speed, 0, 100, 0, 255);

    if (speed > 0) {
        // Aktifkan relay pump
        digitalWrite(PUMP_RELAY_PIN, HIGH);

        // Set direction
        if (forward) {
            digitalWrite(PUMP_MOTOR_IN1_PIN, HIGH);
            digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);
        } else {
            digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);
            digitalWrite(PUMP_MOTOR_IN2_PIN, HIGH);
        }

        // Set speed via PWM
        ledcWrite(PWM_CHANNEL_PUMP, pwmValue);

        Serial.printf("Pump: %s at %d%% speed\n",
                     forward ? "Forward" : "Reverse", speed);
    } else {
        // Stop motor
        digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);
        digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);
        ledcWrite(PWM_CHANNEL_PUMP, 0);
        digitalWrite(PUMP_RELAY_PIN, LOW);

        Serial.println("Pump: Stopped");
    }
}
```

#### Kontrol Fan Motor
```cpp
void setFanMotor(int speed, bool forward) {
    speed = constrain(speed, 0, 100);
    int pwmValue = map(speed, 0, 100, 0, 255);

    if (speed > 0) {
        digitalWrite(FAN_RELAY_PIN, HIGH);

        if (forward) {
            digitalWrite(FAN_MOTOR_IN1_PIN, HIGH);
            digitalWrite(FAN_MOTOR_IN2_PIN, LOW);
        } else {
            digitalWrite(FAN_MOTOR_IN1_PIN, LOW);
            digitalWrite(FAN_MOTOR_IN2_PIN, HIGH);
        }

        ledcWrite(PWM_CHANNEL_FAN, pwmValue);

        Serial.printf("Fan: %s at %d%% speed\n",
                     forward ? "Forward" : "Reverse", speed);
    } else {
        digitalWrite(FAN_MOTOR_IN1_PIN, LOW);
        digitalWrite(FAN_MOTOR_IN2_PIN, LOW);
        ledcWrite(PWM_CHANNEL_FAN, 0);
        digitalWrite(FAN_RELAY_PIN, LOW);

        Serial.println("Fan: Stopped");
    }
}
```

#### Emergency Stop
```cpp
void emergencyStopAllMotors() {
    Serial.println("EMERGENCY STOP - ALL MOTORS!");

    // Stop semua motor immediately
    digitalWrite(PUMP_MOTOR_IN1_PIN, LOW);
    digitalWrite(PUMP_MOTOR_IN2_PIN, LOW);
    digitalWrite(FAN_MOTOR_IN1_PIN, LOW);
    digitalWrite(FAN_MOTOR_IN2_PIN, LOW);

    // Set PWM ke 0
    ledcWrite(PWM_CHANNEL_PUMP, 0);
    ledcWrite(PWM_CHANNEL_FAN, 0);

    // Matikan relay
    digitalWrite(PUMP_RELAY_PIN, LOW);
    digitalWrite(FAN_RELAY_PIN, LOW);
}
```

### Tips Praktis L298N

#### 1. Pemilihan Power Supply
- **Logic Power (5V)**: Minimal 500mA untuk L298N
- **Motor Power (12V)**: Sesuai kebutuhan motor
  * Motor kecil (fan): 12V 1-2A
  * Motor pump: 12V 3-5A
- **Gunakan power supply terpisah** untuk motor dan ESP32

#### 2. Heat Management
- **Heatsink**: Pasang heatsink pada L298N untuk motor besar
- **Ventilasi**: Pastikan sirkulasi udara yang baik
- **Duty Cycle**: Jangan jalankan motor 100% terus-menerus

#### 3. Wiring Best Practices
- **Kabel Motor**: Gunakan kabel yang cukup tebal (minimal AWG 18)
- **Ground**: Pastikan ground ESP32 dan L298N terhubung
- **Decoupling**: Tambahkan kapasitor 100nF di VCC L298N
- **Protection**: Tambahkan dioda flyback pada motor

#### 4. Software Considerations
```cpp
// Soft start untuk motor besar
void softStartMotor(int targetSpeed) {
    for (int speed = 0; speed <= targetSpeed; speed += 5) {
        setPumpMotor(speed, true);
        delay(50);  // Gradual acceleration
    }
}

// Monitoring motor current (jika ada sensor)
void monitorMotorCurrent() {
    float current = readMotorCurrent();
    if (current > MAX_CURRENT) {
        emergencyStopAllMotors();
        Serial.println("Overcurrent detected!");
    }
}
```

### Troubleshooting L298N

#### Problem: Motor tidak berputar
**Possible Causes:**
- Power supply tidak cukup
- Wiring salah
- Motor rusak
- L298N overheating

**Solutions:**
- Check voltage dengan multimeter
- Verify wiring sesuai diagram
- Test motor langsung dengan battery
- Check temperature L298N

#### Problem: Motor berputar lambat
**Possible Causes:**
- PWM value terlalu rendah
- Voltage drop
- Motor overload

**Solutions:**
- Increase PWM value
- Check power supply capacity
- Reduce mechanical load

#### Problem: Motor berputar tidak stabil
**Possible Causes:**
- Noise pada PWM signal
- Ground loop
- Interference

**Solutions:**
- Add decoupling capacitors
- Improve grounding
- Shield motor cables

#### Problem: ESP32 restart saat motor start
**Possible Causes:**
- Power supply sharing
- Voltage drop
- Ground bounce

**Solutions:**
- Use separate power supplies
- Add bulk capacitors
- Improve power distribution

## Cara Penggunaan

1. **Setup Hardware**:

   **Power Supply & Communication:**
   - Hubungkan XY6020 Modbus ke GPIO16 (RX) dan GPIO17 (TX)
   - Hubungkan LCD I2C ke GPIO21 (SDA) dan GPIO22 (SCL)
   - Hubungkan Rotary Encoder ke GPIO2, GPIO4, GPIO15

   **Motor Drivers (L298N):**
   - Water Pump Motor Driver:
     * EN pin ke GPIO5 (PWM speed control)
     * IN1 pin ke GPIO18 (direction control)
     * IN2 pin ke GPIO19 (direction control)
   - Fan Motor Driver:
     * EN pin ke GPIO14 (PWM speed control)
     * IN1 pin ke GPIO27 (direction control)
     * IN2 pin ke GPIO26 (direction control)

   **Sensors:**
   - pH Sensor ke GPIO13 (analog input)
   - Temperature Sensor ke GPIO12 (analog input)
   - MQ Gas Sensor ke GPIO39 (analog input)

2. **Upload Code**:
   ```bash
   pio run --target upload
   ```

3. **Monitor Serial**:
   ```bash
   pio device monitor
   ```

## Next Steps

1. **Testing Hardware**: Test dengan hardware XY6020 yang sebenarnya
2. **Sensor Calibration**: Kalibrasi sensor pH dan temperature
3. **Safety Tuning**: Fine-tuning parameter safety limits
4. **Process Optimization**: Optimasi algoritma kontrol proses

## Dependencies

- `emelianov/modbus-esp8266@^4.1.0` - Untuk komunikasi Modbus
- `igorantolic/Ai Esp32 Rotary Encoder@^1.7` - Rotary encoder
- `marcoschwartz/LiquidCrystal_I2C @ ^1.1.4` - LCD display

## Catatan Penting

- Pastikan wiring XY6020 benar sebelum testing
- Monitor serial output untuk debugging
- Gunakan safety limits yang sesuai untuk aplikasi
- Test dengan beban kecil terlebih dahulu
