#ifndef WEBSERVER_ESP32_H
#define WEBSERVER_ESP32_H

#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>

#include "settings.h"
#include "xy6020.h"
#include "content/result.h"

class XyWebServer {
private:
  WebServer* server;
  Xy6020 *mXy;
  Settings &mConfig;

protected:
  void handleNotFound();
  void handleRoot();
  void handleSettings();
  void handleCharts();
  void handleStyleCss();
  void handleLogicJs();
  void handleSegmentDisplayJs();
  void handleChartsJs();
  void handleControlGet();
  void handleControlPost();
  void handleSettingsGet();
  void handleSettingsPost();
  void handleCORS();
  void handleWifiStatus();

public:
  XyWebServer(Xy6020 *xy_obj, Settings &config);
  void init(bool admin_mode = false);
  void task();
};

#endif // WEBSERVER_ESP32_H
