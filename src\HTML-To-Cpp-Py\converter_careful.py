import os
import glob
import re

content_folder = "../content/" # input folder
result_file = "../content/result.h" # output file
split_lines = False # split output into multiple lines

def process_text(text):
    output = "\""

    for s in text:
        if s == '\f':
            output += "\\f"
        elif s == '\n':
            if split_lines:
                output += "\\n\"\n\""
            else:
                output += "\\n"
        elif s == '\r':
            output += "\\r"
        elif s == '\t':
            output += "\\t"
        elif s == '\"':
            output += "\\\""
        elif s == '\\':
            output += "\\\\"
        else:
            output += s

    output += "\""

    return output

def minify_html(html):
    # Simple HTML minification
    # Remove comments
    html = re.sub(r'<!--(.*?)-->', '', html, flags=re.DOTALL)
    # Remove whitespace between tags
    html = re.sub(r'>\s+<', '><', html)
    # Remove leading and trailing whitespace in lines
    html = re.sub(r'^\s+|\s+$', '', html, flags=re.MULTILINE)
    # Collapse multiple whitespace
    html = re.sub(r'\s{2,}', ' ', html)
    return html

def minify_css(css):
    # Simple CSS minification
    # Remove comments
    css = re.sub(r'/\*.*?\*/', '', css, flags=re.DOTALL)
    # Remove whitespace around braces, colons, semicolons
    css = re.sub(r'\s*{\s*', '{', css)
    css = re.sub(r'\s*}\s*', '}', css)
    css = re.sub(r'\s*:\s*', ':', css)
    css = re.sub(r'\s*;\s*', ';', css)
    css = re.sub(r'\s*,\s*', ',', css)
    # Remove leading and trailing whitespace in lines
    css = re.sub(r'^\s+|\s+$', '', css, flags=re.MULTILINE)
    # Collapse multiple whitespace
    css = re.sub(r'\s{2,}', ' ', css)
    return css

def minify_js(js):
    # Very careful JS minification
    # Only remove comments and extra whitespace
    # Remove single-line comments
    js = re.sub(r'//.*?$', '', js, flags=re.MULTILINE)
    # Remove multi-line comments
    js = re.sub(r'/\*.*?\*/', '', js, flags=re.DOTALL)
    # Remove leading and trailing whitespace in lines
    js = re.sub(r'^\s+|\s+$', '', js, flags=re.MULTILINE)
    # Collapse multiple whitespace, but be careful
    js = re.sub(r'\s{2,}', ' ', js)
    return js

# For the given path, get the List of all files in the directory tree
def getListOfFiles(dirName):
    # create a list of file and sub directories
    # names in the given directory
    listOfFile = os.listdir(dirName)
    allFiles = list()
    # Iterate over all the entries
    for entry in listOfFile:
        # Create full path
        fullPath = os.path.join(dirName, entry)
        # If entry is a directory then get the list of files in this directory
        if os.path.isdir(fullPath):
            allFiles = allFiles + getListOfFiles(fullPath)
        else:
            allFiles.append(fullPath)

    return allFiles

# Get files
files_list_all = getListOfFiles(content_folder)
print(" --- ")

# Clear file
f = open(result_file, "w")
f.write("#ifndef RESULT_H\n#define RESULT_H\n\n")
f.close()

# Statistics
total_before_by_type = { }
total_after_by_type = { }

# Supported file types
text_file_types = ["html", "css", "js", "svg"]

# Process files
for i in files_list_all:
    print("File: " + i)
    file_type = i.split(".")[-1]

    if file_type in text_file_types:
        printed_file_name = file_type + "__" + i.replace(content_folder, "").replace("." + file_type, "").replace(".", "_").replace("\\", "_").replace("/", "_").replace("-", "")

        # Read original file
        f = open(i, "r", encoding="utf-8")
        text = f.read()
        f.close()

        # Save length before processing
        len_before = len(text)

        # For JavaScript files, be extra careful
        if file_type == "js":
            # Only remove comments, preserve everything else
            text = re.sub(r'//.*?$', '', text, flags=re.MULTILINE)
            text = re.sub(r'/\*.*?\*/', '', text, flags=re.DOTALL)
        elif file_type == "html":
            text = minify_html(text)
        elif file_type == "css":
            text = minify_css(text)
        elif file_type == "svg":
            text = re.sub("<!--(.*?)-->", "", text)
        else:
            print("Not supported type")

        len_after = len(text)

        total_after_by_type[file_type] = total_after_by_type.get(file_type, 0) + len_after
        total_before_by_type[file_type] = total_before_by_type.get(file_type, 0) + len_before

        text = process_text(text)

        print("Text size {0} / {1}".format(len_after, len_before))
        print(" --- ")

        # Write to result file
        f = open(result_file, "a")
        f.write("// Length {0} / {1}\n".format(len_after, len_before))
        f.write("const char PROGMEM " + printed_file_name + "[] = "+text+";\n\n")
        f.close()
    else:
        print("Not a text file")

# Close the header file
f = open(result_file, "a")
f.write("#endif // RESULT_H\n")
f.close()

total_before = 0
total_after = 0

print()
print("Saved space by file type: ")

for file_type in total_before_by_type:
    bv = total_before_by_type[file_type]
    av = total_after_by_type[file_type]
    print(" {0}: {1} / {2} ({3})".format(file_type, av, bv, (av-bv)))
    total_before += bv
    total_after += av
print()

print("Total: ")
print(" {0} / {1} ({2})".format(total_after, total_before, (total_after-total_before)))

print()
print(" --- ")
