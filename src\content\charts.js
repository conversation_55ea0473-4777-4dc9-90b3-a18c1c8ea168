// Global variables for charts
let voltageChart;
let currentChart;
let powerChart;
let chartData = {
    voltage: {
        labels: [],
        values: []
    },
    current: {
        labels: [],
        values: []
    },
    power: {
        labels: [],
        values: []
    }
};

// Chart settings
let updateInterval = 1000; // Default: 1 second
let timeRange = 60; // Default: 1 minute (60 seconds)
let maxDataPoints = timeRange;
let isPaused = false;
let updateTimer;

// Initialize charts when page loads
function initCharts() {
    // Initialize connection status check
    getWifiStatus();
    setInterval(function () {
        getWifiStatus();
    }, 5000);
    
    // Create charts
    createVoltageChart();
    createCurrentChart();
    createPowerChart();
    
    // Start data collection
    startDataCollection();
}

// Create voltage chart
function createVoltageChart() {
    const ctx = document.getElementById('voltageChart').getContext('2d');
    voltageChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Voltage (V)',
                data: [],
                borderColor: '#a0a0ff',
                backgroundColor: 'rgba(160, 160, 255, 0.1)',
                borderWidth: 2,
                tension: 0.2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#ccc'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#ccc',
                        maxTicksLimit: 10
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: '#ccc'
                    }
                }
            },
            animation: {
                duration: 0
            }
        }
    });
}

// Create current chart
function createCurrentChart() {
    const ctx = document.getElementById('currentChart').getContext('2d');
    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Current (A)',
                data: [],
                borderColor: '#ffa0a0',
                backgroundColor: 'rgba(255, 160, 160, 0.1)',
                borderWidth: 2,
                tension: 0.2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#ccc'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#ccc',
                        maxTicksLimit: 10
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: '#ccc'
                    }
                }
            },
            animation: {
                duration: 0
            }
        }
    });
}

// Create power chart
function createPowerChart() {
    const ctx = document.getElementById('powerChart').getContext('2d');
    powerChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Power (W)',
                data: [],
                borderColor: '#a0ffa0',
                backgroundColor: 'rgba(160, 255, 160, 0.1)',
                borderWidth: 2,
                tension: 0.2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#ccc'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#ccc',
                        maxTicksLimit: 10
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: '#ccc'
                    }
                }
            },
            animation: {
                duration: 0
            }
        }
    });
}

// Start data collection
function startDataCollection() {
    // Clear any existing timer
    if (updateTimer) {
        clearInterval(updateTimer);
    }
    
    // Set up new timer
    updateTimer = setInterval(function() {
        if (!isPaused) {
            fetchAndUpdateChartData();
        }
    }, updateInterval);
    
    // Initial data fetch
    fetchAndUpdateChartData();
}

// Fetch data and update charts
function fetchAndUpdateChartData() {
    var xhttp = new XMLHttpRequest();
    xhttp.onreadystatechange = function() {
        if (this.readyState == 4 && this.status == 200) {
            try {
                const data = JSON.parse(this.responseText);
                
                // Update connection status
                const connectionState = document.getElementById('connection-state');
                if (data.connected) {
                    connectionState.style.display = 'inline';
                    connectionState.style.color = 'lightgreen';
                } else {
                    connectionState.style.display = 'inline';
                    connectionState.style.color = 'red';
                }
                
                // Add timestamp
                const now = new Date();
                const timeString = now.getHours().toString().padStart(2, '0') + ':' + 
                                  now.getMinutes().toString().padStart(2, '0') + ':' + 
                                  now.getSeconds().toString().padStart(2, '0');
                
                // Update chart data
                updateChartData('voltage', timeString, data.voltage);
                updateChartData('current', timeString, data.current);
                updateChartData('power', timeString, data.power);
                
                // Update charts
                updateCharts();
            } catch (e) {
                console.error('Error parsing data:', e);
            }
        }
    };
    xhttp.open('GET', server_ip + '/control', true);
    xhttp.send();
}

// Update chart data arrays
function updateChartData(type, label, value) {
    // Add new data
    chartData[type].labels.push(label);
    chartData[type].values.push(value);
    
    // Limit data points
    if (chartData[type].labels.length > maxDataPoints) {
        chartData[type].labels.shift();
        chartData[type].values.shift();
    }
}

// Update all charts with current data
function updateCharts() {
    // Update voltage chart
    voltageChart.data.labels = chartData.voltage.labels;
    voltageChart.data.datasets[0].data = chartData.voltage.values;
    voltageChart.update();
    
    // Update current chart
    currentChart.data.labels = chartData.current.labels;
    currentChart.data.datasets[0].data = chartData.current.values;
    currentChart.update();
    
    // Update power chart
    powerChart.data.labels = chartData.power.labels;
    powerChart.data.datasets[0].data = chartData.power.values;
    powerChart.update();
}

// Update chart settings based on user selection
function updateChartSettings() {
    // Get selected values
    updateInterval = parseInt(document.getElementById('update-interval').value);
    timeRange = parseInt(document.getElementById('time-range').value);
    
    // Calculate max data points
    maxDataPoints = Math.ceil(timeRange * 1000 / updateInterval);
    
    // Restart data collection with new interval
    startDataCollection();
}

// Clear all chart data
function clearChartData() {
    // Reset data arrays
    chartData = {
        voltage: {
            labels: [],
            values: []
        },
        current: {
            labels: [],
            values: []
        },
        power: {
            labels: [],
            values: []
        }
    };
    
    // Update charts
    updateCharts();
}

// Toggle pause/resume
function togglePause() {
    isPaused = !isPaused;
    const button = document.getElementById('refresh-button');
    button.textContent = isPaused ? 'Resume' : 'Pause';
}
