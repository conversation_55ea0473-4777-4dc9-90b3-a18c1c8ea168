#include <WiFi.h>
#include <PubSubClient.h>

#include "settings.h"
#include "xy6020.h"

class MqttClient {
private:
  bool mConnected;
  bool mAdminMode;
  unsigned long mLastTimeStamp;
  unsigned long mLastPublishTime;

  WiFiClient mWiFiClient;
  PubSubClient mClient;
  Xy6020 &mXy;
  SettingsData &mCfg;
  Settings &mConfig;
  void reconnect();
  void topicCallback(char *topic, byte *payload, unsigned int length);
  void publishStatus();

public:
  MqttClient(Xy6020 &xy, Settings &config);
  void init(bool admin_mode = false);
  void task();
};
