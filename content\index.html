<!DOCTYPE html>
<html>

<head>
    <script type="text/javascript" src="segment-display.js"></script>
    <script type="text/javascript" src="logic.js"></script>
    <!--link rel="stylesheet" type="text/css" href="siimple.css"-->
    <link rel="stylesheet" type="text/css" href="style.css">
    <title>XY6020 Control</title>
</head>

<body onload="init()">

    <center>
        <div id="main-page">
            <h1 style="font-size: 50px; color: gray">XY6020 Control <span id="connection-state"
                    style="display: inline; color: lightgreen;">&#10003;</span>
            </h1>
            <button class="my-button" id="on-button" onclick="setOutput(1)">ON</button>
            <br>
            <button class="my-button active-button" id="off-button" onclick="setOutput(0)">OFF</button>
            <br>
            <br>
            <button class="my-button" id="settings-button" onclick="goToSettings()">Settings</button>
            <br>
            <br>

            <div class="my-container">
                <span>Actual values</span>
                <table>
                    <tr>
                        <td class="segment-label description">Voltage:</td>
                        <td> <canvas id="actVoltage" width="120" height="34"></canvas> </td>
                        <td class="segment-label unit colored-voltage">V</td>
                    </tr>
                    <tr>
                        <td class="segment-label description">Current:</td>
                        <td> <canvas id="actCurrent" width="120" height="34"></canvas> </td>
                        <td class="segment-label unit colored-current">A</td>
                    </tr>
                    <tr>
                        <td class="segment-label description">Power:</td>
                        <td> <canvas id="actPower" width="120" height="34"></canvas> </td>
                        <td class="segment-label unit colored-power">W</td>
                    </tr>
                    <tr>
                        <td class="segment-label description">Input Voltage:</td>
                        <td> <canvas id="inputVoltage" width="120" height="34"></canvas> </td>
                        <td class="segment-label unit" style="color: #ffffa0">V</td>
                    </tr>
                </table>
            </div>
            <br>
            <div class="my-container">
                <span>Limits</span>
                <table>
                    <tr>
                        <td class="segment-label description">Voltage:</td>
                        <td> <canvas id="targetVoltage" width="120" height="34"></canvas> </td>
                        <td class="segment-label unit">V</td>
                        <td><button class="small-button" id="set-voltage-button"
                                onclick="setTargetValue(this.id)">SET</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="segment-label description">Current:</td>
                        <td> <canvas id="targetCurrent" width="120" height="34"></canvas> </td>
                        <td class="segment-label unit">A</td>
                        <td><button class="small-button" id="set-current-button"
                                onclick="setTargetValue(this.id)">SET</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="segment-label description">Power:</td>
                        <td> <canvas id="targetPower" width="120" height="34"></canvas> </td>
                        <td class="segment-label unit">W</td>
                        <td><button class="small-button" id="set-power-button"
                                onclick="setTargetValue(this.id)">SET</button>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </center>

</body>

</html>