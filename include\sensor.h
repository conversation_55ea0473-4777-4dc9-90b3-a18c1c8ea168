#ifndef SENSOR_H
#define SENSOR_H

#include <Arduino.h>

// Pin definitions untuk Sensors
#define PH_SENSOR_PIN 13        // GPIO13 - pH sensor analog input
#define TEMP_SENSOR_PIN 12      // GPIO12 - Temperature sensor analog input  
#define MQ_SENSOR_PIN 39        // GPIO39 - MQ gas sensor analog input

// ADC settings
#define ADC_RESOLUTION 12       // 12-bit ADC resolution
#define ADC_MAX_VALUE 4095      // Maximum ADC value (2^12 - 1)
#define REFERENCE_VOLTAGE 3.3   // ESP32 reference voltage

// Sensor calibration constants
#define PH_NEUTRAL_VOLTAGE 2.5  // Voltage at pH 7.0
#define PH_SLOPE 0.18          // Voltage change per pH unit
#define TEMP_SCALE 100.0       // Temperature scale factor (for LM35: 100°C/V)

// Struktur untuk sensor readings
struct SensorReadings {
    float pH;
    float temperature;
    float gasLevel;     // MQ sensor reading (0-100%)
    int rawPH;          // Raw ADC value for pH
    int rawTemp;        // Raw ADC value for temperature
    int rawGas;         // Raw ADC value for gas sensor
    float voltagePH;    // Voltage reading for pH
    float voltageTemp;  // Voltage reading for temperature
    float voltageGas;   // Voltage reading for gas sensor
    unsigned long lastUpdate;
    bool isValid;       // Flag to indicate if readings are valid
};

// Global sensor data
extern SensorReadings sensorReadings;

// Sensor initialization and control functions
void initializeSensors();
void readAllSensors();
void calibrateSensors();

// Individual sensor reading functions
float readPHSensor();
float readTemperatureSensor();
float readGasSensor();

// Raw ADC reading functions
int readRawPH();
int readRawTemperature();
int readRawGas();

// Voltage conversion functions
float convertToVoltage(int rawValue);
float convertPHVoltageToValue(float voltage);
float convertTempVoltageToValue(float voltage);
float convertGasVoltageToPercentage(float voltage);

// Sensor validation and filtering
bool validateSensorReadings();
void applySensorFiltering();
float movingAverageFilter(float newValue, float oldAverage, int samples);

// Sensor calibration functions
void calibratePHSensor(float knownPH, float measuredVoltage);
void calibrateTemperatureSensor(float knownTemp, float measuredVoltage);
void resetSensorCalibration();

// Sensor status and diagnostics
void printSensorStatus();
void printRawSensorValues();
void printSensorVoltages();
bool isSensorConnected(int pin);

// Sensor timing functions
void updateSensorReadings();
bool isSensorUpdateNeeded();
void setSensorUpdateInterval(unsigned long interval);

// Sensor error handling
typedef enum {
    SENSOR_OK = 0,
    SENSOR_ERROR_DISCONNECTED,
    SENSOR_ERROR_OUT_OF_RANGE,
    SENSOR_ERROR_CALIBRATION,
    SENSOR_ERROR_TIMEOUT
} SensorError;

SensorError getSensorError();
const char* getSensorErrorString(SensorError error);

#endif // SENSOR_H
