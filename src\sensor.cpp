#include "sensor.h"

// Global sensor data
SensorReadings sensorReadings = {0.0, 0.0, 0.0, 0, 0, 0, 0.0, 0.0, 0.0, 0, false};

// Sensor timing variables
static unsigned long lastSensorUpdate = 0;
static unsigned long sensorUpdateInterval = 1000; // 1 second default

// Sensor error tracking
static SensorError currentSensorError = SENSOR_OK;

// Moving average buffers for filtering
static float pHBuffer[5] = {0};
static float tempBuffer[5] = {0};
static float gasBuffer[5] = {0};
static int bufferIndex = 0;

// Calibration values (can be stored in EEPROM later)
static float pHCalibrationOffset = 0.0;
static float tempCalibrationOffset = 0.0;

void initializeSensors() {
    Serial.println("Initializing Sensor System...");
    
    // Initialize ADC pins
    pinMode(PH_SENSOR_PIN, INPUT);      // GPIO13 - pH sensor
    pinMode(TEMP_SENSOR_PIN, INPUT);    // GPIO12 - Temperature sensor
    pinMode(MQ_SENSOR_PIN, INPUT);      // GPIO39 - MQ gas sensor
    
    // Set ADC resolution to 12-bit
    analogReadResolution(ADC_RESOLUTION);
    
    // Initialize sensor readings
    sensorReadings.lastUpdate = millis();
    sensorReadings.isValid = false;
    
    // Reset error state
    currentSensorError = SENSOR_OK;
    
    // Initialize moving average buffers
    for (int i = 0; i < 5; i++) {
        pHBuffer[i] = 7.0;  // Neutral pH
        tempBuffer[i] = 25.0; // Room temperature
        gasBuffer[i] = 0.0;   // No gas
    }
    
    Serial.println("Sensor System Initialized:");
    Serial.printf("  pH Sensor: GPIO%d\n", PH_SENSOR_PIN);
    Serial.printf("  Temperature Sensor: GPIO%d\n", TEMP_SENSOR_PIN);
    Serial.printf("  MQ Gas Sensor: GPIO%d\n", MQ_SENSOR_PIN);
    Serial.printf("  ADC Resolution: %d-bit\n", ADC_RESOLUTION);
    Serial.printf("  Reference Voltage: %.1fV\n", REFERENCE_VOLTAGE);
}

void readAllSensors() {
    if (!isSensorUpdateNeeded()) {
        return;
    }
    
    // Read raw ADC values
    sensorReadings.rawPH = readRawPH();
    sensorReadings.rawTemp = readRawTemperature();
    sensorReadings.rawGas = readRawGas();
    
    // Convert to voltages
    sensorReadings.voltagePH = convertToVoltage(sensorReadings.rawPH);
    sensorReadings.voltageTemp = convertToVoltage(sensorReadings.rawTemp);
    sensorReadings.voltageGas = convertToVoltage(sensorReadings.rawGas);
    
    // Convert to actual values
    sensorReadings.pH = convertPHVoltageToValue(sensorReadings.voltagePH);
    sensorReadings.temperature = convertTempVoltageToValue(sensorReadings.voltageTemp);
    sensorReadings.gasLevel = convertGasVoltageToPercentage(sensorReadings.voltageGas);
    
    // Apply filtering
    applySensorFiltering();
    
    // Validate readings
    sensorReadings.isValid = validateSensorReadings();
    
    // Update timestamp
    sensorReadings.lastUpdate = millis();
    lastSensorUpdate = millis();
    
    // Debug output (can be disabled in production)
    if (millis() % 5000 < 100) { // Print every 5 seconds
        printSensorStatus();
    }
}

// Individual sensor reading functions
float readPHSensor() {
    int rawValue = readRawPH();
    float voltage = convertToVoltage(rawValue);
    return convertPHVoltageToValue(voltage);
}

float readTemperatureSensor() {
    int rawValue = readRawTemperature();
    float voltage = convertToVoltage(rawValue);
    return convertTempVoltageToValue(voltage);
}

float readGasSensor() {
    int rawValue = readRawGas();
    float voltage = convertToVoltage(rawValue);
    return convertGasVoltageToPercentage(voltage);
}

// Raw ADC reading functions
int readRawPH() {
    return analogRead(PH_SENSOR_PIN);
}

int readRawTemperature() {
    return analogRead(TEMP_SENSOR_PIN);
}

int readRawGas() {
    return analogRead(MQ_SENSOR_PIN);
}

// Voltage conversion functions
float convertToVoltage(int rawValue) {
    return (float)rawValue * (REFERENCE_VOLTAGE / ADC_MAX_VALUE);
}

float convertPHVoltageToValue(float voltage) {
    // Convert voltage to pH using calibration
    // pH = 7.0 + ((neutral_voltage - measured_voltage) / slope)
    float pH = 7.0 + ((PH_NEUTRAL_VOLTAGE - voltage) / PH_SLOPE) + pHCalibrationOffset;
    return constrain(pH, 0.0, 14.0);
}

float convertTempVoltageToValue(float voltage) {
    // Convert voltage to temperature (for LM35: 10mV/°C)
    float temperature = voltage * TEMP_SCALE + tempCalibrationOffset;
    return constrain(temperature, -10.0, 100.0);
}

float convertGasVoltageToPercentage(float voltage) {
    // Convert voltage to gas percentage (0-100%)
    float gasPercentage = (voltage / REFERENCE_VOLTAGE) * 100.0;
    return constrain(gasPercentage, 0.0, 100.0);
}

// Sensor validation and filtering
bool validateSensorReadings() {
    // Check if sensor values are within reasonable ranges
    if (sensorReadings.pH < 0.0 || sensorReadings.pH > 14.0) {
        currentSensorError = SENSOR_ERROR_OUT_OF_RANGE;
        return false;
    }
    
    if (sensorReadings.temperature < -10.0 || sensorReadings.temperature > 100.0) {
        currentSensorError = SENSOR_ERROR_OUT_OF_RANGE;
        return false;
    }
    
    if (sensorReadings.gasLevel < 0.0 || sensorReadings.gasLevel > 100.0) {
        currentSensorError = SENSOR_ERROR_OUT_OF_RANGE;
        return false;
    }
    
    // Check for sensor disconnection (very low or very high readings)
    if (sensorReadings.rawPH < 10 || sensorReadings.rawPH > 4090) {
        currentSensorError = SENSOR_ERROR_DISCONNECTED;
        return false;
    }
    
    currentSensorError = SENSOR_OK;
    return true;
}

void applySensorFiltering() {
    // Apply moving average filter to reduce noise
    sensorReadings.pH = movingAverageFilter(sensorReadings.pH, pHBuffer[bufferIndex], 5);
    sensorReadings.temperature = movingAverageFilter(sensorReadings.temperature, tempBuffer[bufferIndex], 5);
    sensorReadings.gasLevel = movingAverageFilter(sensorReadings.gasLevel, gasBuffer[bufferIndex], 5);
    
    // Update buffer
    pHBuffer[bufferIndex] = sensorReadings.pH;
    tempBuffer[bufferIndex] = sensorReadings.temperature;
    gasBuffer[bufferIndex] = sensorReadings.gasLevel;
    
    bufferIndex = (bufferIndex + 1) % 5;
}

float movingAverageFilter(float newValue, float oldAverage, int samples) {
    return (oldAverage * (samples - 1) + newValue) / samples;
}

// Sensor calibration functions
void calibratePHSensor(float knownPH, float measuredVoltage) {
    // Calculate calibration offset
    float expectedVoltage = PH_NEUTRAL_VOLTAGE - (knownPH - 7.0) * PH_SLOPE;
    pHCalibrationOffset = (expectedVoltage - measuredVoltage) / PH_SLOPE;
    
    Serial.printf("pH Sensor Calibrated: Offset = %.3f\n", pHCalibrationOffset);
}

void calibrateTemperatureSensor(float knownTemp, float measuredVoltage) {
    // Calculate calibration offset
    float expectedVoltage = knownTemp / TEMP_SCALE;
    tempCalibrationOffset = knownTemp - (measuredVoltage * TEMP_SCALE);
    
    Serial.printf("Temperature Sensor Calibrated: Offset = %.3f\n", tempCalibrationOffset);
}

void resetSensorCalibration() {
    pHCalibrationOffset = 0.0;
    tempCalibrationOffset = 0.0;
    Serial.println("Sensor calibration reset to defaults");
}

// Sensor status and diagnostics
void printSensorStatus() {
    Serial.println("=== Sensor Status ===");
    Serial.printf("pH: %.2f (Raw: %d, Voltage: %.3fV)\n", 
                  sensorReadings.pH, sensorReadings.rawPH, sensorReadings.voltagePH);
    Serial.printf("Temperature: %.1f°C (Raw: %d, Voltage: %.3fV)\n", 
                  sensorReadings.temperature, sensorReadings.rawTemp, sensorReadings.voltageTemp);
    Serial.printf("Gas Level: %.1f%% (Raw: %d, Voltage: %.3fV)\n", 
                  sensorReadings.gasLevel, sensorReadings.rawGas, sensorReadings.voltageGas);
    Serial.printf("Valid: %s, Error: %s\n", 
                  sensorReadings.isValid ? "Yes" : "No", 
                  getSensorErrorString(currentSensorError));
    Serial.println("====================");
}

void printRawSensorValues() {
    Serial.printf("Raw ADC - pH: %d, Temp: %d, Gas: %d\n", 
                  sensorReadings.rawPH, sensorReadings.rawTemp, sensorReadings.rawGas);
}

void printSensorVoltages() {
    Serial.printf("Voltages - pH: %.3fV, Temp: %.3fV, Gas: %.3fV\n", 
                  sensorReadings.voltagePH, sensorReadings.voltageTemp, sensorReadings.voltageGas);
}

bool isSensorConnected(int pin) {
    int reading = analogRead(pin);
    return (reading > 10 && reading < 4090); // Not at extremes
}

// Sensor timing functions
void updateSensorReadings() {
    readAllSensors();
}

bool isSensorUpdateNeeded() {
    return (millis() - lastSensorUpdate >= sensorUpdateInterval);
}

void setSensorUpdateInterval(unsigned long interval) {
    sensorUpdateInterval = interval;
}

// Sensor error handling
SensorError getSensorError() {
    return currentSensorError;
}

const char* getSensorErrorString(SensorError error) {
    switch (error) {
        case SENSOR_OK: return "OK";
        case SENSOR_ERROR_DISCONNECTED: return "Disconnected";
        case SENSOR_ERROR_OUT_OF_RANGE: return "Out of Range";
        case SENSOR_ERROR_CALIBRATION: return "Calibration Error";
        case SENSOR_ERROR_TIMEOUT: return "Timeout";
        default: return "Unknown Error";
    }
}

void calibrateSensors() {
    Serial.println("Starting sensor calibration...");
    // Add calibration routine here
    Serial.println("Sensor calibration completed");
}
