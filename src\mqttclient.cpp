#include "mqttclient.h"

#include <ArduinoJson.h>

#define MQTT_MAX_RECEIVE_BUFFER_SIZE 2048

MqttClient::MqttClient(Xy6020 &xy, Settings &config)
    : mConnected(true), mAdminMode(false), mClient(mWiFiClient), mXy(xy),
      mCfg(config.data()), mConfig(config) {}

void MqttClient::reconnect() {
  if (!mClient.connected()) {
    if (mConnected) {
      mConnected = false;
    }

    // Debug info
    Serial.println("MQTT reconnecting...");
    Serial.print("Client ID: ");
    Serial.println(mCfg.mqtt_id);
    Serial.print("Username: ");
    Serial.println(mCfg.mqtt_user);
    Serial.print("Password: ");
    Serial.println(mCfg.mqtt_password);
    Serial.print("Broker: ");
    if (strlen(mCfg.mqtt_broker_host) > 0) {
      Serial.println(mCfg.mqtt_broker_host);
    } else {
      Serial.println(IPAddress(mCfg.mqtt_broker).toString());
    }
    Serial.print("Port: ");
    Serial.println(mCfg.mqtt_port);

    // Siapkan pesan Last Will and Testament (LWT)
    char lwt_topic[150];
    sprintf(lwt_topic, "Pemurni_Emas/status/status");

    JsonDocument lwt_doc;
    lwt_doc["device"] = "XY6020";
    lwt_doc["device_id"] = mCfg.mqtt_id;
    lwt_doc["status"] = "offline";

    String lwt_message;
    serializeJson(lwt_doc, lwt_message);

    // Gunakan QoS level 1 untuk koneksi yang lebih andal dan tambahkan LWT
    if (!mClient.connect(mCfg.mqtt_id, mCfg.mqtt_user, mCfg.mqtt_password,
                         lwt_topic, 1, true, lwt_message.c_str(), true)) {
      Serial.print("MQTT connection failed, rc=");
      Serial.print(mClient.state());
      Serial.println(" retrying in 10 seconds");

      // Print error code meaning
      switch (mClient.state()) {
        case -4: Serial.println("Connection timeout"); break;
        case -3: Serial.println("Connection lost"); break;
        case -2: Serial.println("Connect failed"); break;
        case -1: Serial.println("Disconnected"); break;
        case 1: Serial.println("Bad protocol"); break;
        case 2: Serial.println("Bad client ID"); break;
        case 3: Serial.println("Server unavailable"); break;
        case 4: Serial.println("Bad credentials"); break;
        case 5: Serial.println("Not authorized"); break;
        default: Serial.println("Unknown error");
      }

      yield();
    } else {
      mConnected = true;
      Serial.println("MQTT connected successfully!");

      // Subscribe ke topik yang dikonfigurasi dengan QoS 1
      if (strlen(mCfg.smi_topic) > 0 && mCfg.zero_feed_in) {
        bool subResult = mClient.subscribe(mCfg.smi_topic, 1); // QoS 1
        Serial.print("Subscribed to SMI topic: ");
        Serial.print(mCfg.smi_topic);
        Serial.print(" - Result: ");
        Serial.println(subResult ? "SUCCESS" : "FAILED");
      }

      // Subscribe ke topik kontrol dengan QoS 1
      // Selalu gunakan topik hardcoded untuk mengatasi masalah topik kosong
      const char* subTopic = "Pemurni_Emas/control";
      bool subResult = mClient.subscribe(subTopic, 1); // QoS 1
      Serial.print("Subscribed to control topic (config): ");
      Serial.println(mCfg.mqtt_sub_topic);
      Serial.print("Subscribed to control topic (hardcoded): ");
      Serial.print(subTopic);
      Serial.print(" - Result: ");
      Serial.println(subResult ? "SUCCESS" : "FAILED");

      // Simpan topik hardcoded ke konfigurasi jika kosong
      if (strlen(mCfg.mqtt_sub_topic) == 0) {
        Serial.println("Subscribe topic is empty, setting default topic in configuration");
        strncpy(mCfg.mqtt_sub_topic, subTopic, 128);
        mConfig.store();
        Serial.print("New subscribe topic saved: ");
        Serial.println(mCfg.mqtt_sub_topic);
      }

      // Publish pesan test sederhana
      delay(500);

      // Publish pesan test sederhana untuk memastikan MQTT bekerja
      Serial.println("Publishing test message...");
      bool testResult = mClient.publish("Pemurni_Emas/test", "MQTT Test Message", true);
      Serial.print("Test message result: ");
      Serial.println(testResult ? "SUCCESS" : "FAILED");

      // Publish status awal
      publishStatus();
    }
  }
}

void MqttClient::topicCallback(char *topic, byte *payload,
                               unsigned int length) {
  Serial.print("Received message [");
  Serial.print(topic);
  Serial.println("] ");

  // Buat buffer untuk payload
  char message[length + 1];
  for (unsigned int i = 0; i < length; i++) {
    message[i] = (char)payload[i];
    Serial.print((char)payload[i]);
  }
  message[length] = '\0';
  Serial.println();

  // Proses pesan dari topik SMI untuk zero feed-in
  if (String(topic) == mCfg.smi_topic && mCfg.zero_feed_in) {
    String sensor_name = mCfg.sm_name;
    JsonDocument doc;
    deserializeJson(doc, payload);
    if (doc[sensor_name].isNull() == false) {
      JsonObject sensor = doc[sensor_name].as<JsonObject>();
      if (sensor["Power"].isNull() == false) {
        float power_value = sensor["Power"].as<float>();
        power_value += mXy.actualPower();
        if (power_value > 0) {
          if (power_value > mCfg.max_power) {
            power_value = mCfg.max_power;
          }
          Serial.printf_P("Adjust power value to %0.1f\n", power_value);
          mXy.setPower(power_value);
        }
      }
    }
  }
  // Proses pesan dari topik kontrol - selalu gunakan topik hardcoded
  else if (String(topic) == "Pemurni_Emas/control") {
    // Parse JSON dari pesan
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, message);

    if (error) {
      Serial.print("deserializeJson() failed: ");
      Serial.println(error.c_str());
      return;
    }

    // Proses perintah
    // Perintah langsung
    if (!doc["voltage"].isNull()) {
      float voltage = doc["voltage"];
      Serial.printf_P("MQTT: Setting voltage to %.2fV\n", voltage);
      mXy.setTargetVoltage(voltage);
    }

    if (!doc["current"].isNull()) {
      float current = doc["current"];
      Serial.printf_P("MQTT: Setting current to %.2fA\n", current);
      mXy.setMaxCurrent(current);
    }

    if (!doc["power"].isNull()) {
      float power = doc["power"];
      Serial.printf_P("MQTT: Setting power to %.1fW\n", power);
      mXy.setMaxPower(power);
    }

    if (!doc["output"].isNull()) {
      bool output = doc["output"];
      Serial.printf_P("MQTT: Setting output to %s\n", output ? "ON" : "OFF");
      mXy.setOutputEnabled(output);
    }

    // Perintah dalam format objek
    if (!doc["set"].isNull()) {
      JsonObject set = doc["set"].as<JsonObject>();

      if (!set["voltage"].isNull()) {
        float voltage = set["voltage"];
        Serial.printf_P("MQTT: Setting voltage to %.2fV\n", voltage);
        mXy.setTargetVoltage(voltage);
      }

      if (!set["current"].isNull()) {
        float current = set["current"];
        Serial.printf_P("MQTT: Setting current to %.2fA\n", current);
        mXy.setMaxCurrent(current);
      }

      if (!set["power"].isNull()) {
        float power = set["power"];
        Serial.printf_P("MQTT: Setting power to %.1fW\n", power);
        mXy.setMaxPower(power);
      }

      if (!set["output"].isNull()) {
        bool output = set["output"];
        Serial.printf_P("MQTT: Setting output to %s\n", output ? "ON" : "OFF");
        mXy.setOutputEnabled(output);
      }
    }

    // Perintah sistem
    if (!doc["system"].isNull()) {
      JsonObject system = doc["system"].as<JsonObject>();

      if (!system["restart"].isNull() && system["restart"].as<bool>()) {
        Serial.println("MQTT: Restarting ESP32...");
        // Kirim respons sebelum restart
        char topicBuffer[150];
        sprintf(topicBuffer, "Pemurni_Emas/status/response");
        mClient.publish(topicBuffer, "{\"status\":\"restarting\"}", true);
        delay(500);
        ESP.restart();
      }

      if (!system["request_status"].isNull() && system["request_status"].as<bool>()) {
        Serial.println("MQTT: Status requested, publishing immediately");
        publishStatus();
      }
    }

    // Kirim respons konfirmasi
    char responseTopicBuffer[150];
    sprintf(responseTopicBuffer, "Pemurni_Emas/status/response");

    JsonDocument responseDoc;
    responseDoc["status"] = "success";
    responseDoc["message"] = "Command processed successfully";
    responseDoc["timestamp"] = millis();

    String responseJson;
    serializeJson(responseDoc, responseJson);

    mClient.publish(responseTopicBuffer, responseJson.c_str(), false); // Tidak perlu retained

    // Publish status setelah perubahan
    publishStatus();
  }
}

void MqttClient::publishStatus() {
  // Debug info
  Serial.println("Attempting to publish status...");
  Serial.print("MQTT connected: ");
  Serial.println(mClient.connected() ? "YES" : "NO");
  Serial.print("XY6020 connected: ");
  Serial.println(mXy.isConnected() ? "YES" : "NO");

  // PERBAIKAN: Selalu gunakan topik hardcoded untuk mengatasi masalah topik kosong
  const char* pubTopic = "Pemurni_Emas/status";

  // Untuk debugging
  Serial.print("MQTT topic (config): ");
  Serial.println(mCfg.mqtt_pub_topic);
  Serial.print("MQTT topic (hardcoded): ");
  Serial.println(pubTopic);

  if (!mClient.connected()) {
    Serial.println("Cannot publish: MQTT not connected");
    return;
  }

  // Simpan topik hardcoded ke konfigurasi jika kosong
  if (strlen(mCfg.mqtt_pub_topic) == 0) {
    Serial.println("Topic is empty, setting default topic in configuration");
    strncpy(mCfg.mqtt_pub_topic, pubTopic, 128);
    mConfig.store();
    Serial.print("New topic saved: ");
    Serial.println(mCfg.mqtt_pub_topic);
  }

  // Jika XY6020 tidak terhubung, publikasikan data dummy untuk pengujian
  if (!mXy.isConnected()) {
    Serial.println("XY6020 not connected, publishing dummy data for testing");

    // Buat JSON dengan data dummy
    JsonDocument dummyDoc;
    dummyDoc["voltage"] = 12.34;
    dummyDoc["current"] = 1.23;
    dummyDoc["power"] = 15.2;
    dummyDoc["output"] = true;
    dummyDoc["tvoltage"] = 12.50;
    dummyDoc["tcurrent"] = 2.00;
    dummyDoc["tpower"] = 25.0;
    dummyDoc["ivoltage"] = 24.0;
    dummyDoc["connected"] = false;
    dummyDoc["dummy_data"] = true;

    // Serialize JSON ke string
    String dummyString;
    serializeJson(dummyDoc, dummyString);

    // Publish data dummy
    Serial.print("Publishing dummy data: ");
    Serial.println(dummyString);
    bool dummyResult = mClient.publish(pubTopic, dummyString.c_str(), true);
    Serial.print("Dummy data publish result: ");
    Serial.println(dummyResult ? "SUCCESS" : "FAILED");

    // Publish juga ke topik individual
    char topicBuffer[150];

    // Gunakan topik hardcoded untuk subtopik
    sprintf(topicBuffer, "Pemurni_Emas/status/voltage");
    mClient.publish(topicBuffer, "12.34", true);
    Serial.printf("Published dummy voltage to %s\n", topicBuffer);

    sprintf(topicBuffer, "Pemurni_Emas/status/current");
    mClient.publish(topicBuffer, "1.23", true);
    Serial.printf("Published dummy current to %s\n", topicBuffer);

    sprintf(topicBuffer, "Pemurni_Emas/status/power");
    mClient.publish(topicBuffer, "15.2", true);
    Serial.printf("Published dummy power to %s\n", topicBuffer);

    sprintf(topicBuffer, "Pemurni_Emas/status/output");
    mClient.publish(topicBuffer, "1", true);
    Serial.printf("Published dummy output to %s\n", topicBuffer);

    return;
  }

  // Buat JSON dengan status perangkat
  JsonDocument doc;

  // Get values and print them for debugging
  float voltage = mXy.actualVoltage();
  float current = mXy.actualCurrent();
  float power = mXy.actualPower();
  bool output = mXy.outputEnabled();
  float tvoltage = mXy.targetVoltage();
  float tcurrent = mXy.maxCurrent();
  float tpower = mXy.maxPower();
  float ivoltage = mXy.inputVoltage();
  bool connected = mXy.isConnected();

  Serial.println("XY6020 Values:");
  Serial.print("Voltage: "); Serial.println(voltage);
  Serial.print("Current: "); Serial.println(current);
  Serial.print("Power: "); Serial.println(power);
  Serial.print("Output: "); Serial.println(output);
  Serial.print("Target Voltage: "); Serial.println(tvoltage);
  Serial.print("Target Current: "); Serial.println(tcurrent);
  Serial.print("Target Power: "); Serial.println(tpower);
  Serial.print("Input Voltage: "); Serial.println(ivoltage);

  // Struktur data yang lebih terorganisir untuk integrasi dengan sistem lain
  doc["device"] = "XY6020";
  doc["device_id"] = mCfg.mqtt_id; // Gunakan MQTT client ID sebagai device ID
  doc["firmware_version"] = "1.0.0"; // Tambahkan versi firmware
  doc["timestamp"] = millis(); // Tambahkan timestamp untuk tracking

  // Nilai aktual dalam satu objek
  JsonObject actual = doc.createNestedObject("actual");
  actual["voltage"] = voltage;
  actual["current"] = current;
  actual["power"] = power;

  // Nilai target dalam satu objek
  JsonObject target = doc.createNestedObject("target");
  target["voltage"] = tvoltage;
  target["current"] = tcurrent;
  target["power"] = tpower;

  // Informasi sistem
  JsonObject system = doc.createNestedObject("system");
  system["input_voltage"] = ivoltage;
  system["output_enabled"] = output;
  system["connected"] = connected;

  // Serialize JSON ke string
  String jsonString;
  serializeJson(doc, jsonString);

  Serial.print("JSON to publish: ");
  Serial.println(jsonString);

  // Coba publikasikan dengan format yang berbeda untuk troubleshooting

  // 1. Publikasi dengan format JSON normal (dengan retained flag = true)
  bool publishResult = mClient.publish(pubTopic, jsonString.c_str(), true);
  Serial.print("Publish JSON result: ");
  Serial.println(publishResult ? "SUCCESS" : "FAILED");

  // 2. Publikasi dengan nilai individual untuk troubleshooting
  char valueBuffer[20];
  char topicBuffer[150];

  // Publikasikan voltage sebagai nilai tunggal (dengan retained flag = true)
  sprintf(valueBuffer, "%.2f", voltage);
  sprintf(topicBuffer, "Pemurni_Emas/status/voltage");
  bool voltageResult = mClient.publish(topicBuffer, valueBuffer, true);
  Serial.print("Publish voltage result: ");
  Serial.println(voltageResult ? "SUCCESS" : "FAILED");

  // Publikasikan current sebagai nilai tunggal (dengan retained flag = true)
  sprintf(valueBuffer, "%.2f", current);
  sprintf(topicBuffer, "Pemurni_Emas/status/current");
  bool currentResult = mClient.publish(topicBuffer, valueBuffer, true);
  Serial.print("Publish current result: ");
  Serial.println(currentResult ? "SUCCESS" : "FAILED");

  // Publikasikan power sebagai nilai tunggal (dengan retained flag = true)
  sprintf(valueBuffer, "%.2f", power);
  sprintf(topicBuffer, "Pemurni_Emas/status/power");
  bool powerResult = mClient.publish(topicBuffer, valueBuffer, true);
  Serial.print("Publish power result: ");
  Serial.println(powerResult ? "SUCCESS" : "FAILED");

  // Publikasikan output state sebagai nilai tunggal (dengan retained flag = true)
  sprintf(topicBuffer, "Pemurni_Emas/status/output");
  bool outputResult = mClient.publish(topicBuffer, output ? "1" : "0", true);
  Serial.print("Publish output result: ");
  Serial.println(outputResult ? "SUCCESS" : "FAILED");

  Serial.print("Published status to ");
  Serial.println(pubTopic);
}

void MqttClient::init(bool admin_mode) {
  mAdminMode = admin_mode;
  if (mAdminMode) {
    return;
  }

  // Konfigurasi server MQTT berdasarkan hostname atau IP
  if (strlen(mCfg.mqtt_broker_host) > 0) {
    // Gunakan hostname jika tersedia
    // Hapus "mqtt://" jika ada di awal hostname
    String hostStr = String(mCfg.mqtt_broker_host);
    if (hostStr.startsWith("mqtt://")) {
      hostStr = hostStr.substring(7); // Hapus "mqtt://"
      strncpy(mCfg.mqtt_broker_host, hostStr.c_str(), 128);
      // Simpan perubahan
      mConfig.store();
    }

    // Untuk pengujian, coba gunakan broker alternatif jika broker.emqx.io
    if (String(mCfg.mqtt_broker_host) == "broker.emqx.io") {
      Serial.println("NOTE: Using broker.emqx.io, will also try test.mosquitto.org as fallback");

      // Coba koneksi ke broker.emqx.io terlebih dahulu
      Serial.print("MQTT primary broker: ");
      Serial.println(mCfg.mqtt_broker_host);
      mClient.setServer(mCfg.mqtt_broker_host, mCfg.mqtt_port);

      // Jika gagal terhubung setelah beberapa detik, coba broker alternatif
      unsigned long startTime = millis();
      while (!mClient.connected() && millis() - startTime < 5000) {
        mClient.connect(mCfg.mqtt_id, mCfg.mqtt_user, mCfg.mqtt_password);
        delay(100);
      }

      if (!mClient.connected()) {
        Serial.println("Failed to connect to primary broker, trying fallback...");
        mClient.disconnect();
        delay(100);

        // Coba broker alternatif
        Serial.println("MQTT fallback broker: test.mosquitto.org");
        mClient.setServer("test.mosquitto.org", 1883);
      }
    } else {
      Serial.print("MQTT using hostname: ");
      Serial.println(mCfg.mqtt_broker_host);
      mClient.setServer(mCfg.mqtt_broker_host, mCfg.mqtt_port);
    }
  } else {
    // Gunakan IP address jika hostname tidak tersedia
    Serial.print("MQTT using IP address: ");
    Serial.println(IPAddress(mCfg.mqtt_broker).toString());
    mClient.setServer(IPAddress(mCfg.mqtt_broker), mCfg.mqtt_port);
  }

  mClient.setCallback(std::bind(&MqttClient::topicCallback, this,
                                std::placeholders::_1, std::placeholders::_2,
                                std::placeholders::_3));
  mClient.setBufferSize(MQTT_MAX_RECEIVE_BUFFER_SIZE);
  mLastTimeStamp = millis() - 9000;
  mLastPublishTime = 0;
}

void MqttClient::task() {
  if (mAdminMode) {
    return;
  }

  unsigned long currentTime = millis();

  // Debugging koneksi MQTT setiap 30 detik
  static unsigned long lastDebugTime = 0;
  if (currentTime - lastDebugTime > 30000) {
    lastDebugTime = currentTime;
    Serial.print("MQTT connection state: ");
    if (mClient.connected()) {
      Serial.println("CONNECTED");

      // Publikasikan status koneksi untuk monitoring
      char topicBuffer[150];
      sprintf(topicBuffer, "Pemurni_Emas/status/status");

      JsonDocument statusDoc;
      statusDoc["device"] = "XY6020";
      statusDoc["device_id"] = mCfg.mqtt_id;
      statusDoc["status"] = "online";
      statusDoc["uptime"] = millis() / 1000; // Uptime dalam detik
      statusDoc["ip"] = WiFi.localIP().toString();
      statusDoc["rssi"] = WiFi.RSSI(); // Kekuatan sinyal WiFi

      String statusJson;
      serializeJson(statusDoc, statusJson);

      mClient.publish(topicBuffer, statusJson.c_str(), true);
      Serial.println("Published status message");
    } else {
      Serial.print("DISCONNECTED (");
      Serial.print(mClient.state());
      Serial.println(")");

      // Print error code meaning
      switch (mClient.state()) {
        case -4: Serial.println("Connection timeout"); break;
        case -3: Serial.println("Connection lost"); break;
        case -2: Serial.println("Connect failed"); break;
        case -1: Serial.println("Disconnected"); break;
        case 1: Serial.println("Bad protocol"); break;
        case 2: Serial.println("Bad client ID"); break;
        case 3: Serial.println("Server unavailable"); break;
        case 4: Serial.println("Bad credentials"); break;
        case 5: Serial.println("Not authorized"); break;
        default: Serial.println("Unknown error");
      }
    }
  }

  // Reconnect jika tidak terhubung
  if (!mClient.connected() && currentTime > mLastTimeStamp + 10000) {
    mLastTimeStamp = currentTime;
    reconnect();
  } else {
    mClient.loop();

    // Publish status setiap 10 detik jika terhubung
    if (mClient.connected() && mXy.isConnected() &&
        currentTime > mLastPublishTime + 10000) {
      mLastPublishTime = currentTime;
      publishStatus();
    }
  }
}
