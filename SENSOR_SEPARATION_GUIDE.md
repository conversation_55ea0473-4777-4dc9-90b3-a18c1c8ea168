# Sensor System Separation Guide

## 📋 Overview
Sistem sensor telah dipisahkan dari controlling.cpp ke dalam file terpisah untuk modularitas dan maintainability yang lebih baik.

## 🗂️ File Structure

### Sensor Files
- **`include/sensor.h`** - Header file untuk sistem sensor
- **`src/sensor.cpp`** - Implementasi lengkap sistem sensor

### Updated Files
- **`src/controlling.cpp`** - Fungsi sensor dipindah, hanya tersisa integrasi XY6020
- **`include/controlling.h`** - Pin definitions sensor dipindah ke sensor.h
- **`src/display.cpp`** - Ditambahkan include sensor.h
- **`src/main.cpp`** - Pin rotary encoder dikembalikan ke konfigurasi asli

## 🔧 Pin Configuration (Preserved)

### LCD & Rotary Encoder (Tidak Berubah)
```cpp
// LCD I2C - tetap menggunakan pin default
#define LCD_SDA_PIN 21    // GPIO21
#define LCD_SCL_PIN 22    // GPIO22

// Rotary Encoder - dikembalikan ke konfigurasi asli
#define ROTARY_ENCODER_A_PIN 32     // GPIO32
#define ROTARY_ENCODER_B_PIN 33     // GPIO33  
#define ROTARY_ENCODER_BUTTON_PIN 25 // GPIO25
```

### Sensors (Sesuai Permintaan)
```cpp
#define PH_SENSOR_PIN 13        // GPIO13 - pH sensor
#define TEMP_SENSOR_PIN 12      // GPIO12 - Temperature sensor
#define MQ_SENSOR_PIN 39        // GPIO39 - MQ gas sensor
```

### Motor Control (Sesuai Permintaan)
```cpp
// Fan Motor (L298N)
#define FAN_MOTOR_EN_PIN 14     // GPIO14 - PWM speed
#define FAN_MOTOR_IN1_PIN 27    // GPIO27 - Direction 1
#define FAN_MOTOR_IN2_PIN 26    // GPIO26 - Direction 2

// Water Pump (L298N)  
#define PUMP_MOTOR_EN_PIN 5     // GPIO5 - PWM speed
#define PUMP_MOTOR_IN1_PIN 18   // GPIO18 - Direction 1
#define PUMP_MOTOR_IN2_PIN 19   // GPIO19 - Direction 2
```

## 📊 Sensor System Features

### Data Structure
```cpp
struct SensorReadings {
    float pH;               // pH value (0-14)
    float temperature;      // Temperature in °C
    float gasLevel;         // Gas level (0-100%)
    int rawPH;             // Raw ADC value for pH
    int rawTemp;           // Raw ADC value for temperature
    int rawGas;            // Raw ADC value for gas
    float voltagePH;       // Voltage reading for pH
    float voltageTemp;     // Voltage reading for temperature
    float voltageGas;      // Voltage reading for gas
    unsigned long lastUpdate;
    bool isValid;          // Validation flag
};
```

### Key Functions
```cpp
// Initialization
void initializeSensors();

// Reading Functions
void readAllSensors();
float readPHSensor();
float readTemperatureSensor();
float readGasSensor();

// Calibration
void calibratePHSensor(float knownPH, float measuredVoltage);
void calibrateTemperatureSensor(float knownTemp, float measuredVoltage);
void resetSensorCalibration();

// Diagnostics
void printSensorStatus();
void printRawSensorValues();
bool validateSensorReadings();
```

## 🔄 Integration with Controlling System

### Data Flow
```
sensor.cpp -> sensorReadings (global)
             ↓
controlling.cpp -> sensorData (for compatibility)
                  ↓
display.cpp -> Display on LCD
```

### Integration Code (controlling.cpp)
```cpp
// Update sensor data from sensor system
sensorData.pH = sensorReadings.pH;
sensorData.temperature = sensorReadings.temperature;
sensorData.gasLevel = sensorReadings.gasLevel;
sensorData.lastUpdate = currentTime;

// Update XY6020 sensor data separately
updateXY6020Sensors();
```

## 🛡️ Safety & Validation

### Sensor Validation
- **Range Checking**: pH (0-14), Temperature (-10°C to 100°C), Gas (0-100%)
- **Connection Detection**: Check for sensor disconnection
- **Error Handling**: Comprehensive error reporting
- **Moving Average Filter**: Noise reduction

### Error Types
```cpp
typedef enum {
    SENSOR_OK = 0,
    SENSOR_ERROR_DISCONNECTED,
    SENSOR_ERROR_OUT_OF_RANGE,
    SENSOR_ERROR_CALIBRATION,
    SENSOR_ERROR_TIMEOUT
} SensorError;
```

## 🔧 Calibration System

### pH Sensor Calibration
```cpp
// Example: Calibrate with pH 7.0 buffer solution
float measuredVoltage = 2.5; // Voltage reading at pH 7.0
calibratePHSensor(7.0, measuredVoltage);
```

### Temperature Sensor Calibration
```cpp
// Example: Calibrate with known temperature
float knownTemp = 25.0; // Room temperature
float measuredVoltage = 0.25; // Voltage reading
calibrateTemperatureSensor(knownTemp, measuredVoltage);
```

## 📈 Performance Features

### Timing Control
- **Update Interval**: Configurable (default 1 second)
- **Non-blocking**: Doesn't block main loop
- **Efficient**: Only reads when needed

### Filtering
- **Moving Average**: 5-sample moving average filter
- **Noise Reduction**: Reduces sensor noise
- **Stability**: Improves reading stability

## 🧪 Testing & Debugging

### Debug Functions
```cpp
// Print detailed sensor status
printSensorStatus();

// Print raw ADC values
printRawSensorValues();

// Print voltage readings
printSensorVoltages();

// Check sensor connection
bool connected = isSensorConnected(PH_SENSOR_PIN);
```

### Example Debug Output
```
=== Sensor Status ===
pH: 7.23 (Raw: 2048, Voltage: 2.456V)
Temperature: 24.5°C (Raw: 1024, Voltage: 1.234V)
Gas Level: 15.2% (Raw: 512, Voltage: 0.678V)
Valid: Yes, Error: OK
====================
```

## 🔌 Hardware Compatibility

### Supported Sensors
- **pH Sensors**: Analog pH probes with voltage output
- **Temperature**: LM35, DS18B20 (analog), thermistors
- **Gas Sensors**: MQ series (MQ-2, MQ-3, MQ-4, etc.)

### ADC Configuration
- **Resolution**: 12-bit (0-4095)
- **Reference Voltage**: 3.3V
- **Channels Used**: ADC1_CH5 (GPIO13), ADC2_CH5 (GPIO12), ADC1_CH3 (GPIO39)

## 🚀 Usage Example

### Basic Usage
```cpp
void setup() {
    // Initialize sensor system
    initializeSensors();
}

void loop() {
    // Update sensor readings
    updateSensorReadings();
    
    // Access sensor data
    if (sensorReadings.isValid) {
        Serial.printf("pH: %.2f, Temp: %.1f°C, Gas: %.1f%%\n",
                     sensorReadings.pH, 
                     sensorReadings.temperature,
                     sensorReadings.gasLevel);
    }
    
    delay(1000);
}
```

### Advanced Usage with Calibration
```cpp
void calibrateSensorSystem() {
    Serial.println("Starting sensor calibration...");
    
    // pH calibration with buffer solutions
    Serial.println("Place pH probe in pH 7.0 buffer...");
    delay(30000); // Wait for stabilization
    float voltage7 = convertToVoltage(readRawPH());
    calibratePHSensor(7.0, voltage7);
    
    // Temperature calibration
    Serial.println("Measure ambient temperature...");
    float ambientTemp = 25.0; // Known temperature
    float voltageTemp = convertToVoltage(readRawTemperature());
    calibrateTemperatureSensor(ambientTemp, voltageTemp);
    
    Serial.println("Calibration complete!");
}
```

## ✅ Benefits of Separation

1. **Modularity**: Sensor code isolated from control logic
2. **Maintainability**: Easier to update sensor algorithms
3. **Reusability**: Sensor code can be used in other projects
4. **Testing**: Individual sensor testing possible
5. **Debugging**: Dedicated sensor debugging functions
6. **Calibration**: Comprehensive calibration system
7. **Validation**: Built-in sensor validation and error handling

## 🔄 Backward Compatibility

- **LCD & Rotary Encoder**: Tetap menggunakan konfigurasi asli
- **Display Functions**: Tidak ada perubahan pada tampilan
- **Menu System**: Tetap berfungsi seperti semula
- **Data Access**: sensorData tetap tersedia untuk kompatibilitas

Sistem sensor sekarang terpisah dengan baik namun tetap terintegrasi sempurna dengan sistem controlling dan display yang ada.
