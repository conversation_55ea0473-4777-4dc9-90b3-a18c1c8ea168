# XY6020 Communication and Control Guide
## Berdasarkan xy6020_wifi_ctlr_esp_V1.5 Reference Implementation

### Overview
Implementasi komunikasi dan kontrol XY6020 menggunakan protokol Modbus RTU melalui Serial2 ESP32. Implementasi ini berdasarkan pada contoh xy6020_wifi_ctlr_esp_V1.5 yang telah terbukti stabil dan reliable.

### Hardware Connection
```
ESP32 Pin Configuration:
- GPIO16 (RX2) -> XY6020 TX
- GPIO17 (TX2) -> XY6020 RX  
- GND         -> XY6020 GND
```

### Communication Parameters
- **Baud Rate**: 115200
- **Data Format**: 8N1 (8 data bits, No parity, 1 stop bit)
- **Protocol**: Modbus RTU
- **Slave Address**: 1 (default)
- **Timeout**: 100ms
- **Read Interval**: 500ms

### Register Mapping
```cpp
enum XY6020Register {
    REG_TARGET_VOLTAGE = 0,      // Target voltage (value * 100)
    REG_MAX_CURRENT = 1,         // Max current (value * 100)
    REG_ACTUAL_VOLTAGE = 2,      // Actual voltage (value / 100.0)
    REG_ACTUAL_CURRENT = 3,      // Actual current (value / 100.0)
    REG_ACTUAL_POWER = 4,        // Actual power (value / 10.0)
    REG_INPUT_VOLTAGE = 5,       // Input voltage (value / 100.0)
    REG_OUTPUT_STATE = 18        // Output state (0=OFF, 1=ON)
};
```

### Key Implementation Features

#### 1. Asynchronous Communication
- Non-blocking Modbus communication menggunakan callbacks
- Periodic reading setiap 500ms untuk real-time monitoring
- Automatic error handling dan reconnection

#### 2. Precision Control
- Voltage control: 0-60V dengan resolusi 0.01V
- Current control: 0-20A dengan resolusi 0.01A
- Power monitoring dengan resolusi 0.1W

#### 3. Connection Management
- Real-time connection status monitoring
- Automatic reconnection setiap 5 detik jika terputus
- Error callback untuk debugging

### Core Functions

#### Initialization
```cpp
void XY6020::begin() {
    // Initialize serial communication
    serial->begin(115200, SERIAL_8N1, rxPin, txPin);
    serial->setTimeout(100);
    
    // Initialize Modbus
    modbus.begin(serial);
    modbus.master();
}
```

#### Periodic Task (Must be called in loop)
```cpp
void XY6020::task() {
    // Process Modbus tasks
    modbus.task();
    yield();
    
    // Read registers every 500ms
    if (currentTime - lastReadTime >= 500) {
        lastReadTime = currentTime;
        
        if (!modbus.slave()) {
            modbus.readHreg(slaveAddress, 0, registers, REG_OUTPUT_STATE + 1,
                std::bind(&XY6020::readCallback, this, 
                         std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
        }
    }
}
```

#### Control Functions
```cpp
// Set target voltage (0-60V)
bool setTargetVoltage(float voltage) {
    voltage = constrain(voltage, 0.0, 60.0);
    uint16_t value = (uint16_t)(voltage * 100);
    return writeRegister(REG_TARGET_VOLTAGE, value);
}

// Set max current (0-20A)
bool setMaxCurrent(float current) {
    current = constrain(current, 0.0, 20.0);
    uint16_t value = (uint16_t)(current * 100);
    return writeRegister(REG_MAX_CURRENT, value);
}

// Enable/disable output
bool setOutputEnabled(bool enabled) {
    uint16_t value = enabled ? 1 : 0;
    return writeRegister(REG_OUTPUT_STATE, value);
}
```

#### Reading Functions
```cpp
float getActualVoltage() const { return registers[REG_ACTUAL_VOLTAGE] / 100.0; }
float getActualCurrent() const { return registers[REG_ACTUAL_CURRENT] / 100.0; }
float getActualPower() const { return registers[REG_ACTUAL_POWER] / 10.0; }
bool getOutputState() const { return registers[REG_OUTPUT_STATE] != 0; }
bool isConnected() const { return connectionStatus; }
```

### Error Handling

#### Connection Monitoring
```cpp
bool XY6020::readCallback(Modbus::ResultCode event, uint16_t transactionId, void* data) {
    if (event == Modbus::EX_SUCCESS) {
        connectionStatus = true;
        Serial.printf("XY6020: Read success - V:%.2f I:%.2f P:%.1f\n",
                     getActualVoltage(), getActualCurrent(), getActualPower());
    } else {
        connectionStatus = false;
        Serial.printf("XY6020: Read failed - Error: 0x%02X\n", event);
    }
    return true;
}
```

#### Automatic Reconnection
```cpp
// Try reconnection every 5 seconds if not connected
if (!connectionStatus && (currentTime - lastConnectionAttempt > 5000)) {
    lastConnectionAttempt = currentTime;
    Serial.println("XY6020: Attempting to reconnect...");
    
    // Force a read attempt
    if (!modbus.slave()) {
        modbus.readHreg(slaveAddress, 0, registers, REG_OUTPUT_STATE + 1,
            std::bind(&XY6020::readCallback, this,
                     std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
    }
}
```

### Integration dengan Elektrowining System

#### 1. Dalam main.cpp
```cpp
void setup() {
    // Initialize controlling system (includes XY6020)
    initializeControlling();
}

void loop() {
    // Run controlling system tasks (includes XY6020 communication)
    controllingTask();
}
```

#### 2. Dalam controlling.cpp
```cpp
// Global XY6020 instance
XY6020 xy6020(XY6020_RX_PIN, XY6020_TX_PIN, 1);

void controllingTask() {
    // Update XY6020 communication (CRITICAL - must be called regularly)
    xy6020Task();
    
    // Update sensor data from XY6020
    updateXY6020Sensors();
}

void updateXY6020Sensors() {
    if (xy6020.isConnected()) {
        sensorData.voltage = xy6020.getActualVoltage();
        sensorData.current = xy6020.getActualCurrent();
        sensorData.power = xy6020.getActualPower();
        systemStatus.xy6020Connected = true;
    } else {
        systemStatus.xy6020Connected = false;
    }
}
```

### Best Practices

1. **Always call xy6020.task() in main loop** - Critical untuk komunikasi
2. **Check connection status** sebelum melakukan control operations
3. **Use constrain()** untuk memastikan nilai dalam range yang valid
4. **Implement timeout handling** untuk write operations
5. **Monitor serial communication** untuk debugging
6. **Use callback functions** untuk non-blocking operations

### Troubleshooting

#### Common Issues:
1. **Connection Failed**: Check wiring dan baud rate
2. **Read Timeout**: Periksa slave address dan cable connection
3. **Write Failed**: Pastikan device tidak dalam protection mode
4. **Intermittent Connection**: Check power supply stability

#### Debug Commands:
```cpp
xy6020.printStatus();  // Print detailed status
logSystemStatus();     // Print system overview
```

### Dependencies
```ini
lib_deps =
    emelianov/modbus-esp8266@^4.1.0
```

Implementasi ini telah terbukti stabil dalam xy6020_wifi_ctlr_esp_V1.5 dan dapat diandalkan untuk aplikasi elektrowining yang membutuhkan kontrol voltage dan current yang presisi.
