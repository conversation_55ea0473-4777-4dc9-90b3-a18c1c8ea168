#ifndef RESULT_H
#define RESULT_H

// Length 2850 / 4740
const char PROGMEM html__index[] = "<!DOCTYPE html><html><head><script type=\"text/javascript\" src=\"segment-display.js\"></script><script type=\"text/javascript\" src=\"logic.js\"></script><link rel=\"stylesheet\" type=\"text/css\" href=\"style.css\"><title>XY6020 Control</title></head><body onload=\"init()\"><center><div id=\"main-page\"><h1 style=\"font-size: 50px; color: gray\">XY6020 Control <span id=\"connection-state\"\nstyle=\"display: inline; color: lightgreen;\">&#10003;</span></h1><button class=\"my-button\" id=\"on-button\" onclick=\"setOutput(1)\">ON</button><br><button class=\"my-button active-button\" id=\"off-button\" onclick=\"setOutput(0)\">OFF</button><br><br><button class=\"my-button\" id=\"settings-button\" onclick=\"goToSettings()\">Settings</button><br><button class=\"my-button\" id=\"charts-button\" onclick=\"goToCharts()\">Monitoring</button><br><div class=\"my-container\"><span>WiFi Status</span><table><tr><td class=\"segment-label description\">Status:</td><td id=\"wifi-status\">Not Connected</td></tr><tr><td class=\"segment-label description\">IP Address:</td><td id=\"ip-address\">-</td></tr><tr><td class=\"segment-label description\">SSID:</td><td id=\"wifi-ssid\">-</td></tr></table></div><br><div class=\"my-container\"><span>Actual values</span><table><tr><td class=\"segment-label description\">Voltage:</td><td><canvas id=\"actVoltage\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit colored-voltage\">V</td></tr><tr><td class=\"segment-label description\">Current:</td><td><canvas id=\"actCurrent\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit colored-current\">A</td></tr><tr><td class=\"segment-label description\">Power:</td><td><canvas id=\"actPower\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit colored-power\">W</td></tr><tr><td class=\"segment-label description\">Input Voltage:</td><td><canvas id=\"inputVoltage\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit\" style=\"color: #ffffa0\">V</td></tr></table></div><br><div class=\"my-container\"><span>Limits</span><table><tr><td class=\"segment-label description\">Voltage:</td><td><canvas id=\"targetVoltage\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit\">V</td><td><button class=\"small-button\" id=\"set-voltage-button\"\nonclick=\"setTargetValue(this.id)\">SET</button></td></tr><tr><td class=\"segment-label description\">Current:</td><td><canvas id=\"targetCurrent\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit\">A</td><td><button class=\"small-button\" id=\"set-current-button\"\nonclick=\"setTargetValue(this.id)\">SET</button></td></tr><tr><td class=\"segment-label description\">Power:</td><td><canvas id=\"targetPower\" width=\"120\" height=\"34\"></canvas></td><td class=\"segment-label unit\">W</td><td><button class=\"small-button\" id=\"set-power-button\"\nonclick=\"setTargetValue(this.id)\">SET</button></td></tr></table></div></div></center></body></html>";

// Length 11607 / 11607
const char PROGMEM js__logic[] = "//server_ip = \"http://*************\"\nserver_ip = \"\"\n\nfunction init() {\n    createSegments();\n    getWifiStatus();\n    setInterval(function () {\n        getData();\n    }, 500);\n    setInterval(function () {\n        getWifiStatus();\n    }, 5000);\n}\n\nfunction getWifiStatus() {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            const data = JSON.parse(this.responseText);\n            document.getElementById(\"wifi-status\").textContent = data.status;\n            document.getElementById(\"ip-address\").textContent = data.ip;\n            document.getElementById(\"wifi-ssid\").textContent = data.ssid;\n\n            if (data.status === \"Connected\") {\n                document.getElementById(\"wifi-status\").style.color = \"lightgreen\";\n            } else {\n                document.getElementById(\"wifi-status\").style.color = \"red\";\n            }\n        }\n    };\n    xhttp.open(\"GET\", server_ip + \"/wifi-status\", true);\n    xhttp.send();\n}\n\nfunction getData() {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            const data = JSON.parse(this.responseText);\n            setDisplayValue(displayActVoltage, data.voltage);\n            setDisplayValue(displayActCurrent, data.current);\n            setDisplayValue(displayActPower, data.power);\n            document.getElementById(\"on-button\").classList.remove(\"my-active-button\");\n            document.getElementById(\"off-button\").classList.remove(\"my-active-button\");\n            if (data.output) {\n                document.getElementById(\"on-button\").classList.add(\"my-active-button\");\n            } else {\n                document.getElementById(\"off-button\").classList.add(\"my-active-button\");\n            }\n            setDisplayValue(displayTargetVoltage, data.tvoltage);\n            setDisplayValue(displayTargetCurrent, data.tcurrent);\n            setDisplayValue(displayTargetPower, data.tpower);\n            setDisplayValue(displayInputVoltage, data.ivoltage);\n            item = document.querySelector(\"[id='connection-state']\");\n            if (data.connected) {\n                item.style.display = \"inline\";\n            } else {\n                item.style.display = \"none\";\n            }\n\n        }\n    };\n    xhttp.open(\"GET\", server_ip + \"/control\", true);\n    xhttp.send();\n}\n\n\nfunction getConfig() {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            console.log(this.responseText);\n            const cfg = JSON.parse(this.responseText);\n            console.log(cfg);\n            inp = document.querySelector(\"[name='ssid']\");\n            inp.value = cfg.ssid;\n\n            inp = document.querySelector(\"[name='use-static-ip']\");\n            inp.checked = cfg[\"use-static-ip\"];\n            inp = document.querySelector(\"[name='static-ip']\");\n            inp.value = cfg[\"static-ip\"];\n            inp = document.querySelector(\"[name='subnet']\");\n            inp.value = cfg[\"subnet\"];\n            inp = document.querySelector(\"[name='gateway']\");\n            inp.value = cfg[\"gateway\"];\n\n            inp = document.querySelector(\"[name='mqtt-server']\");\n            inp.value = cfg[\"mqtt-server\"];\n            inp = document.querySelector(\"[name='mqtt-port']\");\n            inp.value = cfg[\"mqtt-port\"];\n            inp = document.querySelector(\"[name='mqtt-user']\");\n            inp.value = cfg[\"mqtt-user\"];\n            inp = document.querySelector(\"[name='mqtt-id']\");\n            inp.value = cfg[\"mqtt-id\"];\n\n            inp = document.querySelector(\"[name='zero-feed-in']\");\n            inp.checked = cfg[\"zero-feed-in\"];\n            inp = document.querySelector(\"[name='smi-topic']\");\n            inp.value = cfg[\"smi-topic\"];\n            inp = document.querySelector(\"[name='sm-name']\");\n            inp.value = cfg[\"sm-name\"];\n\n            inp = document.querySelector(\"[name='enable-input-limits']\");\n            inp.checked = cfg[\"enable-input-limits\"];\n            inp = document.querySelector(\"[name='switch-off-voltage']\");\n            inp.value = cfg[\"switch-off-voltage\"];\n            inp = document.querySelector(\"[name='switch-on-voltage']\");\n            inp.value = cfg[\"switch-on-voltage\"];\n        }\n    };\n    xhttp.open(\"GET\", server_ip + \"/config\", true);\n    xhttp.send();\n}\n\nfunction applySettings(reset) {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            try {\n                var response = JSON.parse(this.responseText);\n                if (response.status === \"OK\") {\n                    // Check if WiFi settings were changed\n                    var wifiChanged = false;\n                    var ssid = document.querySelector(\"[name='ssid']\").value;\n                    var wifiPass = document.querySelector(\"[name='wifi-pass']\").value;\n\n                    if (ssid !== \"\" || wifiPass !== \"\") {\n                        wifiChanged = true;\n                    }\n\n                    if (wifiChanged) {\n                        alert(\"Settings saved! ESP32 will restart to apply WiFi changes. Please reconnect to the new WiFi network.\");\n                    } else {\n                        alert(\"Settings saved successfully!\");\n                    }\n                } else {\n                    alert(\"Applying settings failed!\");\n                }\n            } catch (e) {\n                console.error(\"Error parsing JSON response:\", e);\n                alert(\"Applying settings failed! Error parsing response.\");\n            }\n        }\n    };\n    xhttp.open(\"POST\", server_ip + \"/config\", true);\n    cfg = {};\n    inp = document.querySelector(\"[name='ssid']\");\n    cfg[\"ssid\"] = inp.value;\n    inp = document.querySelector(\"[name='wifi-pass']\");\n    cfg[\"wifi-password\"] = inp.value;\n    inp = document.querySelector(\"[name='use-static-ip']\");\n    cfg[\"use-static-ip\"] = inp.checked;\n    inp = document.querySelector(\"[name='static-ip']\");\n    cfg[\"static-ip\"] = inp.value;\n    inp = document.querySelector(\"[name='subnet']\");\n    cfg[\"subnet\"] = inp.value;\n    inp = document.querySelector(\"[name='gateway']\");\n    cfg[\"gateway\"] = inp.value;\n\n    inp = document.querySelector(\"[name='mqtt-server']\");\n    cfg[\"mqtt-server\"] = inp.value;\n    inp = document.querySelector(\"[name='mqtt-port']\");\n    cfg[\"mqtt-port\"] = parseInt(inp.value);\n    inp = document.querySelector(\"[name='mqtt-user']\");\n    cfg[\"mqtt-user\"] = inp.value;\n    inp = document.querySelector(\"[name='mqtt-pass']\");\n    cfg[\"mqtt-pass\"] = inp.value;\n    inp = document.querySelector(\"[name='mqtt-id']\");\n    cfg[\"mqtt-id\"] = inp.value;\n\n    inp = document.querySelector(\"[name='zero-feed-in']\");\n    cfg[\"zero-feed-in\"] = inp.checked;\n    inp = document.querySelector(\"[name='smi-topic']\");\n    cfg[\"smi-topic\"] = inp.value;\n    inp = document.querySelector(\"[name='sm-name']\");\n    cfg[\"sm-name\"] = inp.value;\n\n    inp = document.querySelector(\"[name='enable-input-limits']\");\n    cfg[\"enable-input-limits\"] = inp.checked;\n    inp = document.querySelector(\"[name='switch-off-voltage']\");\n    cfg[\"switch-off-voltage\"] = inp.value;\n    inp = document.querySelector(\"[name='switch-on-voltage']\");\n    cfg[\"switch-on-voltage\"] = inp.value;\n\n    data = JSON.stringify(cfg);\n    console.log(data);\n    xhttp.send(data);\n}\n\nfunction goBack() {\n    window.location.href = 'index.html';\n}\n\nfunction goToSettings() {\n    window.location.href = 'settings.html';\n}\n\nfunction goToCharts() {\n    window.location.href = 'charts.html';\n}\n\nfunction resetEsp() {\n    var xhttp = new XMLHttpRequest();\n    xhttp.open(\"POST\", server_ip + \"/control?reset\", true);\n    xhttp.send();\n    goBack();\n}\n\nfunction setOutput(state) {\n    var xhttp = new XMLHttpRequest();\n    xhttp.onreadystatechange = function () {\n        if (this.readyState == 4 && this.status == 200) {\n            try {\n                var response = JSON.parse(this.responseText);\n                if (response.status !== \"OK\") {\n                    alert(\"Set output failed!\");\n                }\n            } catch (e) {\n                console.error(\"Error parsing JSON response:\", e);\n                alert(\"Set output failed! Error parsing response.\");\n            }\n        }\n    };\n    xhttp.open(\"POST\", server_ip + \"/control?output=\" + state, true);\n    xhttp.send();\n}\n\nfunction setTargetValue(id) {\n    value = prompt(\"Please enter target value...\");\n    if (value != null && parseFloat(value) != NaN) {\n        var xhttp = new XMLHttpRequest();\n        xhttp.onreadystatechange = function () {\n            if (this.readyState == 4 && this.status == 200) {\n                try {\n                    var response = JSON.parse(this.responseText);\n                    if (response.status !== \"OK\") {\n                        alert(\"Set parameter failed!\");\n                    }\n                } catch (e) {\n                    console.error(\"Error parsing JSON response:\", e);\n                    alert(\"Set parameter failed! Error parsing response.\");\n                }\n            }\n        };\n        var param = \"\";\n        if (id == \"set-voltage-button\") {\n            param = \"voltage\";\n        } else if (id == \"set-current-button\") {\n            param = \"current\";\n        } if (id == \"set-power-button\") {\n            param = \"max-power\";\n        }\n        value = parseFloat(value);\n        xhttp.open(\"POST\", server_ip + \"/control?\" + param + \"=\" + value, true);\n        xhttp.send();\n    }\n}\n\nfunction createSegment(display) {\n    display.pattern = \"###.##\";\n    display.displayAngle = 1.5;\n    display.digitHeight = 21;\n    display.digitWidth = 14;\n    display.digitDistance = 3.1;\n    display.segmentWidth = 2.9;\n    display.segmentDistance = 0.9;\n    display.segmentCount = 7;\n    display.cornerType = 3;\n    display.colorOn = \"#f0f0f0\";\n    display.colorOff = \"#3b3b3b\";\n    display.draw();\n    display.setValue('  0.00');\n}\n\nfunction createSegments() {\n    //actual\n    displayActVoltage = new SegmentDisplay(\"actVoltage\");\n    createSegment(displayActVoltage);\n    displayActVoltage.colorOn = \"#a0a0ff\";\n\n    displayActCurrent = new SegmentDisplay(\"actCurrent\");\n    createSegment(displayActCurrent);\n    displayActCurrent.colorOn = \"#ffa0a0\";\n\n    displayActPower = new SegmentDisplay(\"actPower\");\n    createSegment(displayActPower);\n    displayActPower.colorOn = \"#a0ffa0\";\n    displayActPower.pattern = \"####.#\";\n    displayActPower.setValue('   0.0');\n\n    displayInputVoltage = new SegmentDisplay(\"inputVoltage\");\n    createSegment(displayInputVoltage);\n    displayInputVoltage.colorOn = \"#ffffa0\";\n    setDisplayValue(displayInputVoltage, 0);\n\n    //target\n    displayTargetVoltage = new SegmentDisplay(\"targetVoltage\");\n    createSegment(displayTargetVoltage);\n    displayTargetCurrent = new SegmentDisplay(\"targetCurrent\");\n    createSegment(displayTargetCurrent);\n    displayTargetPower = new SegmentDisplay(\"targetPower\");\n    createSegment(displayTargetPower);\n    displayTargetPower.pattern = \"####.#\";\n    displayTargetPower.setValue('   0.0');\n}\n\nfunction setDisplayValue(display, value) {\n    var pattern_words = display.pattern.split('.');\n    var total_len = display.pattern.length;\n    var post_len = pattern_words[1].length;\n    var value_words = String(value).split('.');\n    var post_word = '';\n    if (value_words.length == 2) {\n        post_word = value_words[1];\n    }\n    post_word = post_word.padEnd(post_len, '0');\n    display.setValue(value_words[0].padStart(total_len - post_len - 1) + '.' + post_word);\n}\n\n";

// Length 13600 / 13600
const char PROGMEM js__segmentdisplay[] = "/*!\n * segment-display.js\n *\n * Copyright 2012, R�diger Appel\n * http://www.3quarks.com\n * Published under Creative Commons 3.0 License.\n *\n * Date: 2012-02-14\n * Version: 1.0.0\n * \n * Dokumentation: http://www.3quarks.com/de/Segmentanzeige\n * Documentation: http://www.3quarks.com/en/SegmentDisplay\n */\n\n// Segment display types\nSegmentDisplay.SevenSegment    = 7;\nSegmentDisplay.FourteenSegment = 14;\nSegmentDisplay.SixteenSegment  = 16;\n\n// Segment corner types\nSegmentDisplay.SymmetricCorner = 0;\nSegmentDisplay.SquaredCorner   = 1;\nSegmentDisplay.RoundedCorner   = 2;\n\n\nfunction SegmentDisplay(displayId) {\n  this.displayId       = displayId;\n  this.pattern         = '##:##:##';\n  this.value           = '12:34:56';\n  this.digitHeight     = 20;\n  this.digitWidth      = 10;\n  this.digitDistance   = 2.5;\n  this.displayAngle    = 12;\n  this.segmentWidth    = 2.5;\n  this.segmentDistance = 0.2;\n  this.segmentCount    = SegmentDisplay.SevenSegment;\n  this.cornerType      = SegmentDisplay.RoundedCorner;\n  this.colorOn         = 'rgb(233, 93, 15)';\n  this.colorOff        = 'rgb(75, 30, 5)';\n};\n\nSegmentDisplay.prototype.setValue = function(value) {\n  this.value = value;\n  this.draw();\n};\n\nSegmentDisplay.prototype.draw = function() {\n  var display = document.getElementById(this.displayId);\n  if (display) {\n    var context = display.getContext('2d');\n    if (context) {\n      // clear canvas\n      context.clearRect(0, 0, display.width, display.height);\n      \n      // compute and check display width\n      var width = 0;\n      var first = true;\n      if (this.pattern) {\n        for (var i = 0; i < this.pattern.length; i++) {\n          var c = this.pattern.charAt(i).toLowerCase();\n          if (c == '#') {\n            width += this.digitWidth;\n          } else if (c == '.' || c == ':') {\n            width += this.segmentWidth;\n          } else if (c != ' ') {\n            return;\n          }\n          width += first ? 0 : this.digitDistance;\n          first = false;\n        }\n      }\n      if (width <= 0) {\n        return;\n      }\n      \n      // compute skew factor\n      var angle = -1.0 * Math.max(-45.0, Math.min(45.0, this.displayAngle));\n      var skew  = Math.tan((angle * Math.PI) / 180.0);\n      \n      // compute scale factor\n      var scale = Math.min(display.width / (width + Math.abs(skew * this.digitHeight)), display.height / this.digitHeight);\n      \n      // compute display offset\n      var offsetX = (display.width - (width + skew * this.digitHeight) * scale) / 2.0;\n      var offsetY = (display.height - this.digitHeight * scale) / 2.0;\n      \n      // context transformation\n      context.save();\n      context.translate(offsetX, offsetY);\n      context.scale(scale, scale);\n      context.transform(1, 0, skew, 1, 0, 0);\n\n      // draw segments\n      var xPos = 0;\n      var size = (this.value) ? this.value.length : 0;\n      for (var i = 0; i < this.pattern.length; i++) {\n        var mask  = this.pattern.charAt(i);\n        var value = (i < size) ? this.value.charAt(i).toLowerCase() : ' ';\n        xPos += this.drawDigit(context, xPos, mask, value);\n      }\n\n      // finish drawing\n      context.restore();\n    }\n  }\n};\n\nSegmentDisplay.prototype.drawDigit = function(context, xPos, mask, c) {\n  switch (mask) {\n    case '#':\n      var r = Math.sqrt(this.segmentWidth * this.segmentWidth / 2.0);\n      var d = Math.sqrt(this.segmentDistance * this.segmentDistance / 2.0);\n      var e = d / 2.0; \n      var f = (this.segmentWidth - d) * Math.sin((45.0 * Math.PI) / 180.0);\n      var g = f / 2.0;\n      var h = (this.digitHeight - 3.0 * this.segmentWidth) / 2.0;\n      var w = (this.digitWidth - 3.0 * this.segmentWidth) / 2.0;\n      var s = this.segmentWidth / 2.0;\n      var t = this.digitWidth / 2.0;\n\n       {\n        var x = xPos;\n        var y = 0;\n        context.fillStyle = this.getSegmentColor(c, '02356789acefp', '02356789abcdefgiopqrstz@');\n        context.beginPath();\n        switch (this.cornerType) {\n          case SegmentDisplay.SymmetricCorner:\n            context.moveTo(x + s + d, y + s);\n            context.lineTo(x + this.segmentWidth + d, y);\n            context.lineTo(x + this.digitWidth - this.segmentWidth - d, y);\n            context.lineTo(x + this.digitWidth - s - d, y + s);\n            break;\n          case SegmentDisplay.SquaredCorner:\n            context.moveTo(x + s + e, y + s - e);\n            context.lineTo(x + this.segmentWidth, y);\n            context.lineTo(x + this.digitWidth - this.segmentWidth, y);\n            context.lineTo(x + this.digitWidth - s - e, y + s - e);\n            break;\n          default:\n            context.moveTo(x + this.segmentWidth - f, y + this.segmentWidth - f - d);\n            context.quadraticCurveTo(x + this.segmentWidth - g, y, x + this.segmentWidth, y);\n            context.lineTo(x + this.digitWidth - this.segmentWidth, y);\n            context.quadraticCurveTo(x + this.digitWidth - this.segmentWidth + g, y, x + this.digitWidth - this.segmentWidth + f, y + this.segmentWidth - f - d);\n        }\n        context.lineTo(x + this.digitWidth - this.segmentWidth - d, y + this.segmentWidth);\n        context.lineTo(x + this.segmentWidth + d, y + this.segmentWidth);\n        context.fill();\n      }\n      \n      // draw segment b\n      x = xPos + this.digitWidth - this.segmentWidth;\n      y = 0;\n      context.fillStyle = this.getSegmentColor(c, '01234789adhpy', '01234789abdhjmnopqruwy');\n      context.beginPath();\n      switch (this.cornerType) {\n        case SegmentDisplay.SymmetricCorner:\n          context.moveTo(x + s, y + s + d);\n          context.lineTo(x + this.segmentWidth, y + this.segmentWidth + d);\n          break;\n        case SegmentDisplay.SquaredCorner:\n          context.moveTo(x + s + e, y + s + e);\n          context.lineTo(x + this.segmentWidth, y + this.segmentWidth);\n          break;\n        default:\n          context.moveTo(x + f + d, y + this.segmentWidth - f);\n          context.quadraticCurveTo(x + this.segmentWidth, y + this.segmentWidth - g, x + this.segmentWidth, y + this.segmentWidth);\n      }\n      context.lineTo(x + this.segmentWidth, y + h + this.segmentWidth - d);\n      context.lineTo(x + s, y + h + this.segmentWidth + s - d);\n      context.lineTo(x, y + h + this.segmentWidth - d);\n      context.lineTo(x, y + this.segmentWidth + d);\n      context.fill();\n      \n      // draw segment c\n      x = xPos + this.digitWidth - this.segmentWidth;\n      y = h + this.segmentWidth;\n      context.fillStyle = this.getSegmentColor(c, '013456789abdhnouy', '01346789abdghjmnoqsuw@', '%');\n      context.beginPath();\n      context.moveTo(x, y + this.segmentWidth + d);\n      context.lineTo(x + s, y + s + d);\n      context.lineTo(x + this.segmentWidth, y + this.segmentWidth + d);\n      context.lineTo(x + this.segmentWidth, y + h + this.segmentWidth - d);\n      switch (this.cornerType) {\n        case SegmentDisplay.SymmetricCorner:\n          context.lineTo(x + s, y + h + this.segmentWidth + s - d);\n          context.lineTo(x, y + h + this.segmentWidth - d);\n          break;\n        case SegmentDisplay.SquaredCorner:\n          context.lineTo(x + s + e, y + h + this.segmentWidth + s - e);\n          context.lineTo(x, y + h + this.segmentWidth - d);\n          break;\n        default:\n          context.quadraticCurveTo(x + this.segmentWidth, y + h + this.segmentWidth + g, x + f + d, y + h + this.segmentWidth + f); //\n          context.lineTo(x, y + h + this.segmentWidth - d);\n      }\n      context.fill();\n      \n {\n        x = xPos;\n        y = this.digitHeight - this.segmentWidth;\n        context.fillStyle = this.getSegmentColor(c, '0235689bcdelotuy_', '0235689bcdegijloqsuz_=@');\n        context.beginPath();\n        context.moveTo(x + this.segmentWidth + d, y);\n        context.lineTo(x + this.digitWidth - this.segmentWidth - d, y);\n        switch (this.cornerType) {\n          case SegmentDisplay.SymmetricCorner:\n            context.lineTo(x + this.digitWidth - s - d, y + s);\n            context.lineTo(x + this.digitWidth - this.segmentWidth - d, y + this.segmentWidth);\n            context.lineTo(x + this.segmentWidth + d, y + this.segmentWidth);\n            context.lineTo(x + s + d, y + s);\n            break;\n          case SegmentDisplay.SquaredCorner:\n            context.lineTo(x + this.digitWidth - s - e, y + s + e);\n            context.lineTo(x + this.digitWidth - this.segmentWidth, y + this.segmentWidth);\n            context.lineTo(x + this.segmentWidth, y + this.segmentWidth);\n            context.lineTo(x + s + e, y + s + e);\n            break;\n          default:\n            context.lineTo(x + this.digitWidth - this.segmentWidth + f, y + f + d);\n            context.quadraticCurveTo(x + this.digitWidth - this.segmentWidth + g, y + this.segmentWidth, x + this.digitWidth - this.segmentWidth, y + this.segmentWidth);\n            context.lineTo(x + this.segmentWidth, y + this.segmentWidth);\n            context.quadraticCurveTo(x + this.segmentWidth - g, y + this.segmentWidth, x + this.segmentWidth - f, y + f + d);\n            context.lineTo(x + this.segmentWidth - f, y + f + d);\n        }\n        context.fill();\n      }\n      \n      // draw segment e\n      x = xPos;\n      y = h + this.segmentWidth;\n      context.fillStyle = this.getSegmentColor(c, '0268abcdefhlnoprtu', '0268acefghjklmnopqruvw@');\n      context.beginPath();\n      context.moveTo(x, y + this.segmentWidth + d);\n      context.lineTo(x + s, y + s + d);\n      context.lineTo(x + this.segmentWidth, y + this.segmentWidth + d);\n      context.lineTo(x + this.segmentWidth, y + h + this.segmentWidth - d);\n      switch (this.cornerType) {\n        case SegmentDisplay.SymmetricCorner:\n          context.lineTo(x + s, y + h + this.segmentWidth + s - d);\n          context.lineTo(x, y + h + this.segmentWidth - d);\n          break;\n        case SegmentDisplay.SquaredCorner:\n          context.lineTo(x + s - e, y + h + this.segmentWidth + s - d + e);\n          context.lineTo(x, y + h + this.segmentWidth);\n          break;\n        default:\n          context.lineTo(x + this.segmentWidth - f - d, y + h + this.segmentWidth + f); \n          context.quadraticCurveTo(x, y + h + this.segmentWidth + g, x, y + h + this.segmentWidth);\n      }\n      context.fill();\n      \n      // draw segment f\n      x = xPos;\n      y = 0;\n      context.fillStyle = this.getSegmentColor(c, '045689abcefhlpty', '045689acefghklmnopqrsuvwy@', '%');\n      context.beginPath();\n      context.moveTo(x + this.segmentWidth, y + this.segmentWidth + d);\n      context.lineTo(x + this.segmentWidth, y + h + this.segmentWidth - d);\n      context.lineTo(x + s, y + h + this.segmentWidth + s - d);\n      context.lineTo(x, y + h + this.segmentWidth - d);\n      switch (this.cornerType) {\n        case SegmentDisplay.SymmetricCorner:\n          context.lineTo(x, y + this.segmentWidth + d);\n          context.lineTo(x + s, y + s + d);\n          break;\n        case SegmentDisplay.SquaredCorner:\n          context.lineTo(x, y + this.segmentWidth);\n          context.lineTo(x + s - e, y + s + e);\n          break;\n        default:\n          context.lineTo(x, y + this.segmentWidth);\n          context.quadraticCurveTo(x, y + this.segmentWidth - g, x + this.segmentWidth - f - d, y + this.segmentWidth - f); \n          context.lineTo(x + this.segmentWidth - f - d, y + this.segmentWidth - f); \n      }\n      context.fill();\n\n      // draw segment g for 7 segments\n      if (this.segmentCount == 7) {\n        x = xPos;\n        y = (this.digitHeight - this.segmentWidth) / 2.0;\n        context.fillStyle = this.getSegmentColor(c, '2345689abdefhnoprty-=');\n        context.beginPath();\n        context.moveTo(x + s + d, y + s);\n        context.lineTo(x + this.segmentWidth + d, y);\n        context.lineTo(x + this.digitWidth - this.segmentWidth - d, y);\n        context.lineTo(x + this.digitWidth - s - d, y + s);\n        context.lineTo(x + this.digitWidth - this.segmentWidth - d, y + this.segmentWidth);\n        context.lineTo(x + this.segmentWidth + d, y + this.segmentWidth);\n        context.fill();\n      }\n            \n      \n      return this.digitDistance + this.digitWidth;\n      \n    case '.':\n      context.fillStyle = (c == '#') || (c == '.') ? this.colorOn : this.colorOff;\n      this.drawPoint(context, xPos, this.digitHeight - this.segmentWidth, this.segmentWidth);\n      return this.digitDistance + this.segmentWidth;\n      \n    case ':':\n      context.fillStyle = (c == '#') || (c == ':') ? this.colorOn : this.colorOff;\n      var y = (this.digitHeight - this.segmentWidth) / 2.0 - this.segmentWidth;\n      this.drawPoint(context, xPos, y, this.segmentWidth);\n      this.drawPoint(context, xPos, y + 2.0 * this.segmentWidth, this.segmentWidth);\n      return this.digitDistance + this.segmentWidth;\n      \n    default:\n      return this.digitDistance;    \n  }\n};\n\nSegmentDisplay.prototype.drawPoint = function(context, x1, y1, size) {\n  var x2 = x1 + size;\n  var y2 = y1 + size;\n  var d  = size / 4.0;\n  \n  context.beginPath();\n  context.moveTo(x2 - d, y1);\n  context.quadraticCurveTo(x2, y1, x2, y1 + d);\n  context.lineTo(x2, y2 - d);\n  context.quadraticCurveTo(x2, y2, x2 - d, y2);\n  context.lineTo(x1 + d, y2);\n  context.quadraticCurveTo(x1, y2, x1, y2 - d);\n  context.lineTo(x1, y1 + d);\n  context.quadraticCurveTo(x1, y1, x1 + d, y1);\n  context.fill();\n}; \n\nSegmentDisplay.prototype.getSegmentColor = function(c, charSet7, charSet14, charSet16) {\n  if (c == '#') {\n    return this.colorOn;\n  } else {\n    switch (this.segmentCount) {\n      case 7:  return (charSet7.indexOf(c) == -1) ? this.colorOff : this.colorOn;\n      default: return this.colorOff;\n    }\n  }\n};\n\n\n\n\n";

// Length 3479 / 5377
const char PROGMEM html__settings[] = "<!DOCTYPE html><html><head><script type=\"text/javascript\" src=\"logic.js\"></script><link rel=\"stylesheet\" type=\"text/css\" href=\"style.css\"><title>XY6020 Settings</title></head><body onload=\"getConfig()\"><center><h1 style=\"font-size: 50px; color: gray\">XY6020 Settings</h1><div id=\"settings-page\"><div class=\"my-container\"><span>WiFi</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">SSID:</span></td><td style=\"width: 70%\"><input type=\"text\" class=\"my-input\" name=\"ssid\" placeholder=\"SSID\"></td></tr><tr><td><span class=\"my-label\">Password:</span></td><td><input type=\"password\" class=\"my-input\" name=\"wifi-pass\"></td></tr></table></div></div><div class=\"my-container\"><span>DHCP</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">Use static ip address</span></td><td style=\"width: 70%\"><input type=\"checkbox\" id=\"use-static-ip\" name=\"use-static-ip\"\nclass=\"switch\" /></td></tr><tr><td><span class=\"my-label\">IP address:</span></td><td><input type=\"text\" class=\"my-input\" name=\"static-ip\"></td></tr><tr><td><span class=\"my-label\">Subnet mask:</span></td><td><input type=\"text\" class=\"my-input\" name=\"subnet\"></td></tr><tr><td><span class=\"my-label\">Gateway:</span></td><td><input type=\"text\" class=\"my-input\" name=\"gateway\"></td></tr></table></div><div class=\"my-container\"><span>MQTT</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">Server ip:</span></td><td style=\"width: 70%\"><input type=\"text\" class=\"my-input\" name=\"mqtt-server\"></td></tr><tr><td><span class=\"my-label\">Server port:</span></td><td><input type=\"text\" class=\"my-input\" name=\"mqtt-port\"></td></tr><tr><td><span class=\"my-label\">User:</span></td><td><input type=\"text\" class=\"my-input\" name=\"mqtt-user\" placeholder=\"user\"></td></tr><tr><td><span class=\"my-label\">Password:</span></td><td><input type=\"password\" class=\"my-input\" name=\"mqtt-pass\"></td></tr><tr><td><span class=\"my-label\">Client ID:</span></td><td><input type=\"text\" class=\"my-input\" name=\"mqtt-id\" placeholder=\"xy6020_MMMMMMMMMMMM\"></td></tr></table></div><div class=\"my-container\"><span>Electricity feed-in</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">Zero feed-in mode</span></td><td style=\"width: 70%\"><input type=\"checkbox\" id=\"checkbox0\" name=\"zero-feed-in\" class=\"switch\" /></td></tr><tr><td><span class=\"my-label\">Tasmota SMI MQTT topic:</span></td><td><input type=\"text\" class=\"my-input\" name=\"smi-topic\"></td></tr><tr><td><span class=\"my-label\">Smart meter name:</span></td><td><input type=\"text\" class=\"my-input\" name=\"sm-name\"></td></tr></table></div><div class=\"my-container\"><span>Input voltage limits</span><table class=\"my-param-table\"><tr><td><span class=\"my-label\">Enable limits</span></td><td style=\"width: 70%\"><input type=\"checkbox\" id=\"checkbox0\" name=\"enable-input-limits\"\nclass=\"switch\" /></td></tr><tr><td><span class=\"my-label\">Switch off lower voltage:</span></td><td><input type=\"number\" class=\"my-input\" name=\"switch-off-voltage\"></td></tr><tr><td><span class=\"my-label\">Switch on over voltage:</span></td><td><input type=\"text\" class=\"my-input\" name=\"switch-on-voltage\"></td></tr></table></div><br><br><br><div style=\"width: 60%; margin:0px; padding: 0px;\"><button class=\" my-button small\" id=\"back-button\" onclick=\"goBack()\">Back</button><button class=\" my-button small\" id=\"apply-button\" onclick=\"applySettings()\">Apply</button><button class=\" my-button small\" id=\"reset-button\" onclick=\"resetEsp()\">Reboot</button></div></center></body></html>";

// Length 2622 / 3481
const char PROGMEM css__style[] = "body{background-color:#272624;color:white;}.my-label{align-items:left;color:inherit;display:flex;font-size:1rem;font-weight:bold;min-width:0px;padding-bottom:0.25rem;padding-top:0.25rem;font-size:20px;vertical-align:middle;margin:10px;}.my-input{background-color:#363f4f;border-color:transparent;border-style:solid;border-width:0.125rem;border-radius:0.5rem;box-sizing:border-box;color:inherit;display:block;font-family:inherit;font-size:inherit;font-weight:normal;line-height:inherit;margin:3px;margin-left:10px;min-width:0px;outline:0px;padding:0.05rem 0.5rem;width:100%;}.my-input:focus{border-color:#1c76fd;}.switch{appearance:none;box-sizing:border-box;color:#1c76fd;cursor:pointer;display:inline-block;height:1.25rem;position:relative;width:2rem;}.switch:after{background-color:#fff;border-radius:100%;content:'';display:block;height:0.875rem;left:0.1875rem;position:absolute;top:0.1875rem;transition:left 0.3s ease;width:0.875rem;z-index:2;}.switch:before{background-color:#363f4f;border-radius:2rem;content:'';display:block;height:100%;left:0px;position:absolute;top:0px;transition:background-color 0.3s ease;width:100%;z-index:1;}.switch:checked:after{left:1rem;}.switch:checked:before{background-color:currentColor;}.my-button a{color:white;text-decoration:none;text-decoration-color:red;}.my-container{width:50%;display:inline-block;border:2px solid grey;border-radius:10px;margin:3px;margin-left:auto;margin-right:auto;padding:15px;padding-top:0px;}.my-param-table{width:100%;}.my-container>span:first-child{color:#ccc;display:block;border-top-left-radius:10px;border-top-right-radius:10px;margin-bottom:30px;margin-left:-15px;margin-right:-15px;padding-left:10px;background-color:#5b5e55;text-align:left;font-weight:bold;font-size:32px;}.small-button:hover{background-color:#2d2f2d;font-weight:bold;}.small-button{background-color:#1d1f1d;border:none;border-radius:7px;color:white;padding:10px 10px;text-align:center;text-decoration:none;display:inline-block;font-size:15px;}.my-button.small{width:320px;}.my-button{width:413px;background-color:#383b38;border:none;border-radius:7px;color:white;padding:20px 120px;text-align:center;text-decoration:none;display:inline-block;font-size:20px;font-weight:bold;}.my-active-button:hover{background-color:#5c665c !important;}.my-button:hover{background-color:#2d2f2d;}.my-active-button{background-color:#869486;}.segment-label{font-size:28px;font-weight:bold;position:relative;bottom:3px;}.unit{font-size:40px;}.description{color:gray;text-align:right;padding-right:15px;}.colored-voltage{color:#a0a0ff;}.colored-current{color:#ffa0a0;}.colored-power{color:#a0ffa0;}";

// Length 3000 / 4000
const char PROGMEM html__charts[] = "<!DOCTYPE html><html><head><script type=\"text/javascript\" src=\"https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js\"></script><script type=\"text/javascript\" src=\"charts.js\"></script><script type=\"text/javascript\" src=\"logic.js\"></script><link rel=\"stylesheet\" type=\"text/css\" href=\"style.css\"><title>XY6020 Monitoring</title><style>.chart-container{position:relative;height:250px;width:100%;margin-bottom:20px;}.chart-controls{margin-bottom:20px;display:flex;justify-content:space-between;align-items:center;}.chart-controls select,.chart-controls input{background-color:#363f4f;border:none;color:white;padding:5px 10px;border-radius:5px;margin-left:10px;}.chart-controls label{color:#ccc;margin-right:5px;}</style></head><body onload=\"initCharts()\"><center><h1 style=\"font-size:50px;color:gray\">XY6020 Monitoring <span id=\"connection-state\" style=\"display:inline;color:lightgreen;\">&#10003;</span></h1><div class=\"my-container\" style=\"width:80%;\"><span>Real-time Monitoring</span><div class=\"chart-controls\"><div><label for=\"update-interval\">Update Interval:</label><select id=\"update-interval\" onchange=\"updateChartSettings()\"><option value=\"500\">0.5 seconds</option><option value=\"1000\" selected>1 second</option><option value=\"2000\">2 seconds</option><option value=\"5000\">5 seconds</option></select></div><div><label for=\"time-range\">Time Range:</label><select id=\"time-range\" onchange=\"updateChartSettings()\"><option value=\"30\">30 seconds</option><option value=\"60\" selected>1 minute</option><option value=\"300\">5 minutes</option><option value=\"600\">10 minutes</option></select></div><div><button class=\"small-button\" onclick=\"clearChartData()\">Clear Data</button></div></div><div class=\"chart-container\"><canvas id=\"voltageChart\"></canvas></div><div class=\"chart-container\"><canvas id=\"currentChart\"></canvas></div><div class=\"chart-container\"><canvas id=\"powerChart\"></canvas></div></div><br><div style=\"width:60%;margin:0px;padding:0px;\"><button class=\"my-button small\" id=\"back-button\" onclick=\"goBack()\">Back</button><button class=\"my-button small\" id=\"refresh-button\" onclick=\"togglePause()\">Pause</button></div></center></body></html>";

// Length 9000 / 12000
const char PROGMEM js__charts[] = "let voltageChart;let currentChart;let powerChart;let chartData={voltage:{labels:[],values:[]},current:{labels:[],values:[]},power:{labels:[],values:[]}};let updateInterval=1000;let timeRange=60;let maxDataPoints=timeRange;let isPaused=false;let updateTimer;function initCharts(){getWifiStatus();setInterval(function(){getWifiStatus();},5000);createVoltageChart();createCurrentChart();createPowerChart();startDataCollection();}function createVoltageChart(){const ctx=document.getElementById('voltageChart').getContext('2d');voltageChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'Voltage (V)',data:[],borderColor:'#a0a0ff',backgroundColor:'rgba(160, 160, 255, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:false,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function createCurrentChart(){const ctx=document.getElementById('currentChart').getContext('2d');currentChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'Current (A)',data:[],borderColor:'#ffa0a0',backgroundColor:'rgba(255, 160, 160, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:true,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function createPowerChart(){const ctx=document.getElementById('powerChart').getContext('2d');powerChart=new Chart(ctx,{type:'line',data:{labels:[],datasets:[{label:'Power (W)',data:[],borderColor:'#a0ffa0',backgroundColor:'rgba(160, 255, 160, 0.1)',borderWidth:2,tension:0.2,fill:true}]},options:{responsive:true,maintainAspectRatio:false,scales:{y:{beginAtZero:true,grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc'}},x:{grid:{color:'rgba(255, 255, 255, 0.1)'},ticks:{color:'#ccc',maxTicksLimit:10}}},plugins:{legend:{labels:{color:'#ccc'}}},animation:{duration:0}}});}function startDataCollection(){if(updateTimer){clearInterval(updateTimer);}updateTimer=setInterval(function(){if(!isPaused){fetchAndUpdateChartData();}},updateInterval);fetchAndUpdateChartData();}function fetchAndUpdateChartData(){var xhttp=new XMLHttpRequest();xhttp.onreadystatechange=function(){if(this.readyState==4&&this.status==200){try{const data=JSON.parse(this.responseText);const connectionState=document.getElementById('connection-state');if(data.connected){connectionState.style.display='inline';connectionState.style.color='lightgreen';}else{connectionState.style.display='inline';connectionState.style.color='red';}const now=new Date();const timeString=now.getHours().toString().padStart(2,'0')+':'+now.getMinutes().toString().padStart(2,'0')+':'+now.getSeconds().toString().padStart(2,'0');updateChartData('voltage',timeString,data.voltage);updateChartData('current',timeString,data.current);updateChartData('power',timeString,data.power);updateCharts();}catch(e){console.error('Error parsing data:',e);}}};xhttp.open('GET',server_ip+'/control',true);xhttp.send();}function updateChartData(type,label,value){chartData[type].labels.push(label);chartData[type].values.push(value);if(chartData[type].labels.length>maxDataPoints){chartData[type].labels.shift();chartData[type].values.shift();}}function updateCharts(){voltageChart.data.labels=chartData.voltage.labels;voltageChart.data.datasets[0].data=chartData.voltage.values;voltageChart.update();currentChart.data.labels=chartData.current.labels;currentChart.data.datasets[0].data=chartData.current.values;currentChart.update();powerChart.data.labels=chartData.power.labels;powerChart.data.datasets[0].data=chartData.power.values;powerChart.update();}function updateChartSettings(){updateInterval=parseInt(document.getElementById('update-interval').value);timeRange=parseInt(document.getElementById('time-range').value);maxDataPoints=Math.ceil(timeRange*1000/updateInterval);startDataCollection();}function clearChartData(){chartData={voltage:{labels:[],values:[]},current:{labels:[],values:[]},power:{labels:[],values:[]}};updateCharts();}function togglePause(){isPaused=!isPaused;const button=document.getElementById('refresh-button');button.textContent=isPaused?'Resume':'Pause';}";

#endif // RESULT_H
