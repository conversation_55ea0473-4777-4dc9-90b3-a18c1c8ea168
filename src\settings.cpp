#include "settings.h"

#include <EEPROM.h>
#include <WiFi.h>
#include <nvs_flash.h>
#include <Preferences.h>

#ifndef EEPROM_SIZE
#define EEPROM_SIZE 4096
#endif

#define STORAGE_NAMESPACE "xy6020"

Settings::Settings() {
  // Initialize NVS
  esp_err_t err = nvs_flash_init();
  if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
    // NVS partition was truncated and needs to be erased
    Serial.println("NVS partition needs to be erased, doing so now...");
    ESP_ERROR_CHECK(nvs_flash_erase());
    err = nvs_flash_init();
  }
  ESP_ERROR_CHECK(err);

  // Try to initialize EEPROM
  if (!EEPROM.begin(EEPROM_SIZE)) {
    Serial.println("Failed to initialize EEPROM, trying to recover...");
    // Try to recover by using Preferences instead
    mUsePreferences = true;
  } else {
    mUsePreferences = false;
  }
}

void Settings::load() {
  bool configValid = false;

  if (mUsePreferences) {
    // Use Preferences if EEPROM failed
    Preferences prefs;
    if (prefs.begin(STORAGE_NAMESPACE, true)) { // Read-only mode
      if (prefs.isKey("config")) {
        size_t dataSize = prefs.getBytesLength("config");
        if (dataSize == sizeof(SettingsData)) {
          prefs.getBytes("config", &mData, sizeof(SettingsData));
          configValid = (mData.version == CONFIG_VERSION);
        }
      }
      prefs.end();
    }
  } else {
    // Try EEPROM first
    EEPROM.get(0, mData);
    configValid = (mData.version == CONFIG_VERSION);
  }

  if (!configValid) {
    Serial.println("\nNo valid config detected -> Setting Defaults");
    setDefaults();
    store(); // Save defaults immediately
  } else {
    Serial.println("Configuration loaded successfully");
  }
}

void Settings::store() {
  bool success = false;

  if (mUsePreferences) {
    // Use Preferences if EEPROM failed
    Preferences prefs;
    if (prefs.begin(STORAGE_NAMESPACE, false)) { // Read-write mode
      success = prefs.putBytes("config", &mData, sizeof(SettingsData));
      prefs.end();
    }
  } else {
    // Try EEPROM first
    if (EEPROM.begin(EEPROM_SIZE)) {
      EEPROM.put(0, mData);
      success = EEPROM.commit();
    }
  }

  if (success) {
    Serial.println("Configuration saved successfully");
  } else {
    Serial.println("Failed to save configuration!");
  }
}

void Settings::setDefaults() {
  mData.version = CONFIG_VERSION;
  sprintf(mData.wifi_ssid, "MY-SSID");
  mData.wifi_password[0] = '\0';

  mData.use_static_ip = false;
  mData.static_ip = 0;
  mData.subnet = 0xffffff00;
  mData.gateway = 0;

  mData.mqtt_broker = 0;
  // Gunakan broker lokal sebagai default
  sprintf(mData.mqtt_broker_host, "*************"); // Ganti dengan alamat IP komputer Anda
  mData.mqtt_port = 1883;
  mData.mqtt_user[0] = '\0'; // Kosongkan username untuk broker lokal
  mData.mqtt_password[0] = '\0'; // Kosongkan password untuk broker lokal

  uint8_t mac[6];
  WiFi.macAddress(mac);
  sprintf(mData.mqtt_id, "xy6020_%02X%02X%02X%02X%02X%02X", mac[0], mac[1],
          mac[2], mac[3], mac[4], mac[5]);

  // Default untuk topik MQTT publish/subscribe
  sprintf(mData.mqtt_pub_topic, "Pemurni_Emas/status");
  sprintf(mData.mqtt_sub_topic, "Pemurni_Emas/control");

  mData.zero_feed_in = false;
  sprintf(mData.smi_topic, "tele/tasmota_smi_device/SENSOR");
  sprintf(mData.sm_name, "SmartMeter_1st_FLOOR");

  mData.enable_input_limits = false;
  mData.switch_off_voltage = 0;
  mData.switch_on_voltage = 0.1;

  mData.max_power = 1200;
}

String Settings::ipToString(uint32_t ip) {
  char str[16];
  sprintf(str, "%d.%d.%d.%d", 0xff & (ip >> 24), 0xff & (ip >> 16),
          0xff & (ip >> 8), 0xff & (ip >> 0));
  return String(str);
}
