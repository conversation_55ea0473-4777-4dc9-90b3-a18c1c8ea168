#ifndef DISPLAY_H
#define DISPLAY_H

#include <LiquidCrystal_I2C.h>
#include <Preferences.h>
#include "AiEsp32RotaryEncoder.h"
#include "menu.h"
#include "controlling.h"

// Eksternal variabel yang dibutuhkan
extern LiquidCrystal_I2C lcd;
extern Preferences preferences;
extern AiEsp32RotaryEncoder rotaryEncoder;
extern MenuState menuState;
extern float pH, temp, current;
extern int pumpSpeed, fanSpeed, voltage;
extern float pHMin, pHMax, tempMin, tempMax, gasMin, gasMax;
extern int pumpSpeedSetting, fanTimeSetting;
extern float voltageSetting, currentSetting;
extern int currentMenuSize;
extern int previousEncoderPosition;
extern bool displayNeedsUpdate;
extern int userNameIndex, profileTypeIndex;
extern UserProfile profiles[];
extern int profileCount, currentProfileIndex;
extern UserProfile tempProfile;
extern bool isRunning;
extern const char* profileTypes[];

// Eksternal variabel dari controlling system
extern SystemStatus systemStatus;
extern SensorData sensorData;
extern ControlSettings controlSettings;

// Deklarasi fungsi display
void displayMainScreen();
void displaySetupMenu();
void displayManualSettingsMenu();
void displaySensorSettingsMenu();
void displayOutputSettingsMenu();
void displayConfigProfilesMenu();
void displayUserAMenu();
void displayNewUserMenu();
void displayPumpSettingMenu();
void displayFanSettingMenu();
void displayVoltageControlMenu();
void displayCurrentControlMenu();
void displayEditSensorMenu();
void displayAddUserMenu();
void displayStartMenu();
void displayChooseProfileMenu();
String formatFloat(float value, int decimalPlaces);

// Deklarasi fungsi rotary encoder dan pengelolaan profil
void IRAM_ATTR readEncoderISR();
void rotary_loop();
void rotary_onButtonClick();
void updateEncoderBoundaries();
void initializeProfiles();
void saveProfileToPreferences(int index);
void loadProfilesFromPreferences();
void saveProfileCountToPreferences();

#endif