#ifndef XY6020_H
#define XY6020_H

#include <Arduino.h>
#include <ModbusRTU.h>
#include <HardwareSerial.h>

// XY6020 Register definitions
enum XY6020Register {
    REG_TARGET_VOLTAGE = 0,
    REG_MAX_CURRENT = 1,
    REG_ACTUAL_VOLTAGE = 2,
    REG_ACTUAL_CURRENT = 3,
    REG_ACTUAL_POWER = 4,
    REG_INPUT_VOLTAGE = 5,
    REG_OUTPUT_CHARGE = 6,
    REG_OUTPUT_CHARGE_HIGH = 7,
    REG_OUTPUT_ENERGY = 8,
    REG_OUTPUT_ENERGY_HIGH = 9,
    REG_ON_TIME_HOURS = 10,
    REG_ON_TIME_MINUTES = 11,
    REG_ON_TIME_SECONDS = 12,
    REG_INTERNAL_TEMP = 13,
    REG_EXTERNAL_TEMP = 14,
    REG_KEY_LOCK = 15,
    REG_PROTECTION_STATUS = 16,
    REG_CV_CC_STATE = 17,
    REG_OUTPUT_STATE = 18
};

class XY6020 {
private:
    HardwareSerial* serial;
    ModbusRTU modbus;
    uint16_t registers[20];
    uint8_t slaveAddress;
    bool connectionStatus;
    unsigned long lastReadTime;
    int rxPin, txPin;
    
    // Callback functions
    bool readCallback(Modbus::ResultCode event, uint16_t transactionId, void* data);
    bool writeCallback(Modbus::ResultCode event, uint16_t transactionId, void* data);
    
    void waitForTransaction();
    bool writeRegister(uint16_t reg, uint16_t value);

public:
    XY6020(int rx_pin, int tx_pin, uint8_t slave_addr = 1);
    
    // Initialization and task functions
    void begin();
    void task();
    
    // Status functions
    bool isConnected() const { return connectionStatus; }
    
    // Control functions
    bool setTargetVoltage(float voltage);
    bool setMaxCurrent(float current);
    bool setOutputEnabled(bool enabled);
    
    // Reading functions
    float getTargetVoltage() const { return registers[REG_TARGET_VOLTAGE] / 100.0; }
    float getMaxCurrent() const { return registers[REG_MAX_CURRENT] / 100.0; }
    float getActualVoltage() const { return registers[REG_ACTUAL_VOLTAGE] / 100.0; }
    float getActualCurrent() const { return registers[REG_ACTUAL_CURRENT] / 100.0; }
    float getActualPower() const { return registers[REG_ACTUAL_POWER] / 10.0; }
    float getInputVoltage() const { return registers[REG_INPUT_VOLTAGE] / 100.0; }
    bool getOutputState() const { return registers[REG_OUTPUT_STATE] != 0; }
    
    // Utility functions
    void printStatus();
};

#endif // XY6020_H
